/*jslint newcap: true */
/*global XMLHttpRequest: false, FormData: false */
/*
 * Inline Text Attachment
 *
 * Author: <PERSON>
 * Contact: <EMAIL>
 */
(function(document, window) {
  'use strict';

  var inlineAttachment = function(options, instance) {
    this.settings = inlineAttachment.util.merge(options, inlineAttachment.defaults);
    this.editor = instance;
    this.filenameTag = '{filename}';
    this.lastValue = null;
  };

  /**
   * Will holds the available editors
   *
   * @type {Object}
   */
  inlineAttachment.editors = {};

  /**
   * Utility functions
   */
  inlineAttachment.util = {

    /**
     * Simple function to merge the given objects
     *
     * @param {Object[]} object Multiple object parameters
     * @returns {Object}
     */
    merge: function() {
      var result = {};
      for (var i = arguments.length - 1; i >= 0; i--) {
        var obj = arguments[i];
        for (var k in obj) {
          if (obj.hasOwnProperty(k)) {
            result[k] = obj[k];
          }
        }
      }
      return result;
    },

    /**
     * Append a line of text at the bottom, ensuring there aren't unnecessary newlines
     *
     * @param {String} appended Current content
     * @param {String} previous Value which should be appended after the current content
     */
    appendInItsOwnLine: function(previous, appended) {
      return (previous + "\n\n[[D]]" + appended)
        .replace(/(\n{2,})\[\[D\]\]/, "\n\n")
        .replace(/^(\n*)/, "");
    },

    /**
     * Inserts the given value at the current cursor position of the textarea element
     *
     * @param  {HtmlElement} el
     * @param  {String} value Text which will be inserted at the cursor position
     */
    insertTextAtCursor: function(el, text) {
      var scrollPos = el.scrollTop,
        strPos = 0,
        browser = false,
        range;

      if ((el.selectionStart || el.selectionStart === '0')) {
        browser = "ff";
      } else if (document.selection) {
        browser = "ie";
      }

      if (browser === "ie") {
        el.focus();
        range = document.selection.createRange();
        range.moveStart('character', -el.value.length);
        strPos = range.text.length;
      } else if (browser === "ff") {
        strPos = el.selectionStart;
      }

      var front = (el.value).substring(0, strPos);
      var back = (el.value).substring(strPos, el.value.length);
      el.value = front + text + back;
      strPos = strPos + text.length;
      if (browser === "ie") {
        el.focus();
        range = document.selection.createRange();
        range.moveStart('character', -el.value.length);
        range.moveStart('character', strPos);
        range.moveEnd('character', 0);
        range.select();
      } else if (browser === "ff") {
        el.selectionStart = strPos;
        el.selectionEnd = strPos;
        el.focus();
      }
      el.scrollTop = scrollPos;
    }
  };

  /**
   * Default configuration options
   *
   * @type {Object}
   */
  inlineAttachment.defaults = {
    /**
     * Allowed MIME types
     */
    allowedTypes: [
      'image/jpeg',
      'image/png',
      'image/jpg',
      'image/gif',
      'video/mp4',
      'video/mkv',
      'video/avi',
      'video/webm',
      'video/quicktime', // .mov files
      'video/x-msvideo', // .avi alternative
      'audio/mpeg',
      'audio/mp3',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/pdf'
    ],

    /**
     * Text which will be inserted when dropping or pasting a file.
     * Acts as a placeholder which will be replaced when the file is done with uploading
     */
    progressText: '![Uploading file... 0%]()',

    /**
     * When a file has successfully been uploaded the progressText
     * will be replaced by the urlText, the {filename} tag will be replaced
     * by the filename that has been returned by the server
     */
    urlText: "![file]({filename})",

    /**
     * Before the file is send
     */
    beforeFileUpload: function() {
      return true;
    },

    /**
     * Triggers when a file is dropped or pasted
     */
    onFileReceived: function() {},

    /**
     * Custom upload handler
     *
     * @return {Boolean} when false is returned it will prevent default upload behavior
     */
    onFileUploadResponse: function() {
      return true;
    },

    /**
     * Custom error handler. Runs after removing the placeholder text and before the alert().
     * Return false from this function to prevent the alert dialog.
     *
     * @return {Boolean} when false is returned it will prevent default error behavior
     */
    onFileUploadError: function() {
      return true;
    },

    /**
     * When a file has succesfully been uploaded
     */
    onFileUploaded: function() {}
  };

  /**
   * Uploads the blob
   *
   * @param  {Blob} file blob data received from event.dataTransfer object
   * @return {XMLHttpRequest} request object which sends the file
   */
  inlineAttachment.prototype.uploadFile = async function(file) {
    const response = await uploadFile(file, this.updateUploadStatus(this),);
    if (!response[0]) return this.onFileUploadError();
    const text = this.editor.getValue().replace(this.lastValue, `![${file.name}](${response[1]})`);
    return this.editor.setValue(text);
  };

  /**
   * Returns if the given file is allowed to handle
   *
   * @param {File} clipboard data file
   */
  inlineAttachment.prototype.isFileAllowed = function(file) {
    if (file.kind === 'string') { return false; }
    if (this.settings.allowedTypes.indexOf('*') === 0){
      return true;
    } else {
      return this.settings.allowedTypes.indexOf(file.type) >= 0;
    }
  };

  /**
   * Called when a file has failed to upload
   *
   * @param  {XMLHttpRequest} xhr
   * @return {Void}
   */
  inlineAttachment.prototype.onFileUploadError = function() {
    var text = this.editor.getValue().replace(this.lastValue, "![Failed to upload]()");
    this.editor.setValue(text);
  };

  /**
   * Called when a file has been inserted, either by drop or paste
   *
   * @param  {File} file
   * @return {Void}
   */
  inlineAttachment.prototype.onFileInserted = function(file) {
    if (this.settings.onFileReceived.call(this, file) !== false) {
      this.lastValue = this.settings.progressText;
      this.editor.insertValue(this.lastValue);
    }
  };


  /**
   * Called when a paste event occured
   * @param  {Event} e
   * @return {Boolean} if the event was handled
   */
  inlineAttachment.prototype.onPaste = function(e) {
    var result = false,
      clipboardData = e.clipboardData,
      items;

    if (typeof clipboardData === "object") {
      items = clipboardData.items || clipboardData.files || [];

      for (var i = 0; i < items.length; i++) {
        var item = items[i];
        if (this.isFileAllowed(item)) {
          result = true;
          this.onFileInserted(item.getAsFile());
          this.uploadFile(item.getAsFile());
        }
      }
    }

    if (result) { e.preventDefault(); }

    return result;
  };

  /**
   * Called when a drop event occures
   * @param  {Event} e
   * @return {Boolean} if the event was handled
   */
  inlineAttachment.prototype.onDrop = function(e) {
    var result = false;
    for (var i = 0; i < e.dataTransfer.files.length; i++) {
      var file = e.dataTransfer.files[i];
      if (this.isFileAllowed(file)) {
        result = true;
        this.onFileInserted(file);
        this.uploadFile(file);
      }
    }

    return result;
  };

  inlineAttachment.prototype.updateUploadStatus = (me) => () => {
    const xhr = new window.XMLHttpRequest();
    xhr.upload.onprogress = function (evt) {
        if (!evt.lengthComputable) return;
        const percentComplete = Math.floor((evt.loaded / evt.total) * 100);
        if (percentComplete === 100) return;
        const newValue = `![Uploading file... ${percentComplete}%]()`;
        const text = me.editor.getValue().replace(me.lastValue, newValue);
        me.lastValue = newValue;
        me.editor.setValue(text);
    };

    return xhr;
  };

  window.inlineAttachment = inlineAttachment;

})(document, window);

async function saveSecureUploadAPI(params) {
  try {
    const data = await $.ajax({
        method: 'POST',
        url: `/api/admin/secure-upload/files?organisationId=${USER_SESSION.organisationId}`,
        data: params,
    });
    return [true, data];
  } catch (err) {
      console.error(_get(err, 'responseJSON.message', 'Something went wrong'));
      return [false];
  }
}

async function uploadToS3API(tokenUrl, formData, xhr) {
  try {
    const response = await $.ajax({
        url: tokenUrl,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr,
    });
    return [true, response];
  } catch (err) {
    console.error(_get(err, 'responseJSON.message', 'Something went wrong'));
    return [false];
  }
}

async function getSecureUploadTokenAPI(fileName) {
  try {
    const data = await $.ajax({
        method: 'GET',
        url: `/api/admin/secure-upload/token?fileName=${fileName}&organisationId=${USER_SESSION.organisationId}`,
    });
    return [true, data];
  } catch (err) {
      console.error(_get(err, 'responseJSON.message', 'Something went wrong'));
      return [false];
  }
}

async function uploadFile(file, onStatusUpdate) {
  console.log(file)
  const tokenRes = await getSecureUploadTokenAPI(file.name);
  if (!tokenRes[0]) return tokenRes;
  const { key, url: uploadUrl, fields } = tokenRes[1];

  const formData = new FormData();
  Object.entries(fields).forEach(([k, value]) => {
      formData.append(k, value);
  });
  formData.append('file', file);

  const s3Resp = await uploadToS3API(uploadUrl, formData, onStatusUpdate);
  if (!s3Resp[0]) return s3Resp;

  const fileType = key.split('/')[0];
  const postRes = await saveSecureUploadAPI({
      key,
      name: file.name,
      size: file.size,
      type: fileType,
  });

  if (!postRes[0]) return postRes;
  return [true, `/api/admin/secure-upload/private/${key}?organisationId=${USER_SESSION.organisationId}`];
}