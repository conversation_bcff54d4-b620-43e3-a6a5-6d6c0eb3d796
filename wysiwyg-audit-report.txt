WYSIWYG Video Upload and Rendering Audit Report

  Critical Issues Identified

  1. Element Replacement Problem (Primary Issue)

  Location: trumbowyg.upload.js:391 - $placeholder.replaceWith(video)

  Problem: The video container structure is completely destroyed during state transitions:
  - Line 296-305: Creates <div class="video-embed-container" data-video-url="${url}">
  - Line 344: Finds placeholder inside container
  - Line 391: $placeholder.replaceWith(video) - This replaces the loading placeholder with a video element
  - Line 412: $placeholder.replaceWith(errorMsg) - On error, replaces with error message

  Critical Flaw: When video fails to upload/load/process, the data-video-url attribute on the parent container is preserved, but the internal structure becomes inconsistent. More importantly, if the parent
  container gets removed or replaced elsewhere, the entire video URL reference is lost.

  2. Inconsistent Video Processing Architectures

  WYSIWYG Approach (trumbowyg.upload.js):

  - Upload: S3 secure upload via /api/admin/secure-upload/files
  - Rendering: Blob URL creation using fetch() + URL.createObjectURL()
  - Error Handling: Basic try/catch with element replacement
  - Container: <div class="video-embed-container" data-video-url="${url}">

  Markdown Approach (openKB.js + video-renderer.js):

  - Upload: Same S3 secure upload
  - Rendering: Streaming URL construction via VideoRenderer
  - Error Handling: Comprehensive retry logic, fallback URLs, processing state management
  - Container: <div class="file-to-proxy" data-url="${src}"> → processed by VideoRenderer

  3. Error Handling Gaps

  A. No Processing State Management

  WYSIWYG has no handling for:
  - Videos in "to process" state
  - Videos in "processing" state
  - Long-running video processing (60+ seconds)
  - Server-side processing errors

  B. Blob URL Failure Recovery

  When fetch() fails in trumbowyg.upload.js:398-413:
  .catch(error => {
      const errorMsg = $('<div>', {
          html: 'Failed to load video',
          css: { padding: '1rem', color: '#666', textAlign: 'center', fontStyle: 'italic' }
      });
      $placeholder.replaceWith(errorMsg);  // ❌ Destroys retry possibility
  });

  C. No Retry Mechanisms

  - Failed videos cannot be retried
  - No automatic retry for network failures
  - No user-initiated retry options

  4. Integration Inconsistencies

  A. Dual Video Systems

  - WYSIWYG: Manual blob URL creation
  - Markdown: VideoRenderer with streaming URLs
  - Result: Different video capabilities and error handling

  B. Missing VideoRenderer Integration

  WYSIWYG editor processes videos in edit-article.js:35-40:
  window.videoRenderer.activateVideoPlayers({
      targetContainers: ['.trumbowyg-editor'],
      staggerDelay: 200,
      retryDelay: 200
  });

  But this only works for existing video containers, not newly uploaded ones from the upload plugin.

  5. State Persistence Issues

  A. Session Tracking Problems

  // trumbowyg.upload.js:394-396
  if (url && window.processedVideos) {
      window.processedVideos.add(url);
  }
  This tracks processed videos but doesn't help with failed uploads.

  B. Container Structure Loss

  When blob creation fails, the original video URL is preserved in data-video-url, but there's no mechanism to retry using that URL.

  Root Cause Analysis

  The fundamental issue is architectural inconsistency:

  1. WYSIWYG uses legacy blob URL approach while Markdown uses modern streaming VideoRenderer
  2. Element replacement destroys retry capability - once a video fails, the container structure doesn't support re-processing
  3. No shared error handling between upload plugin and video renderer
  4. Missing integration between S3 upload success and VideoRenderer processing

  Impact Assessment

  High Impact Issues:

  - Videos that fail to upload/process become permanently broken in WYSIWYG mode
  - Users lose their video content when processing fails
  - No user feedback for processing states
  - Inconsistent video experience between Markdown and WYSIWYG

  Medium Impact Issues:

  - Poor error messages for failed videos
  - No retry mechanisms for transient failures
  - Different video capabilities between editor modes

  Recommended Solution Approach

  Phase 1: Fix Element Persistence (Critical - 1-2 hours)

  1. Modify trumbowyg.upload.js to preserve container structure:
    - Replace content inside video-embed-container, not the container itself
    - Maintain data-video-url through all state transitions
    - Add retry button in error states

  Phase 2: Integrate VideoRenderer (Major - 4-6 hours)

  1. Replace blob URL approach with VideoRenderer streaming:
    - After S3 upload success, let VideoRenderer handle the video processing
    - Remove manual fetch() + createObjectURL() logic
    - Use consistent video container structure

  Phase 3: Unified Error Handling (Medium - 2-3 hours)

  1. Standardize error handling across both editor modes
  2. Add processing state feedback for long-running video processing
  3. Implement retry mechanisms for failed videos

  This approach would solve your core issue of losing video URLs when uploads fail, while also modernizing the WYSIWYG video handling to match the improved Markdown system.

> proceed with your solution

⏺ User approved Claude's plan:
  ⎿  WYSIWYG Video Upload Fix Implementation Plan

     Phase 1: Fix Element Persistence (Critical)

     Goal: Prevent video URL loss when uploads fail

     1.1 Modify trumbowyg.upload.js video container structure (30 mins)

     - Change element replacement logic to preserve parent container
     - Update error handling to maintain data-video-url attribute
     - Add retry button in error states

     1.2 Add container content management (15 mins)

     - Replace .replaceWith() calls with .html() updates
     - Ensure video-embed-container persists through all state changes
     - Maintain consistent container structure

     Phase 2: Integrate VideoRenderer (Major)

     Goal: Unify video processing between WYSIWYG and Markdown

     2.1 Replace blob URL approach with VideoRenderer (45 mins)

     - Remove manual fetch() + createObjectURL() logic from trumbowyg.upload.js
     - After S3 upload success, trigger VideoRenderer processing
     - Use consistent .file-to-proxy or .video-embed-container structure

     2.2 Update video activation in edit-article.js (15 mins)

     - Ensure VideoRenderer processes newly uploaded videos in WYSIWYG
     - Add proper delay/retry for upload-to-processing pipeline
     - Remove duplicate video processing logic

     Phase 3: Enhanced Error Handling (Medium)

     Goal: Provide better user experience for failed videos

     3.1 Add processing state feedback (30 mins)

     - Show processing status for videos in "processing" state
     - Add loading indicators for long-running processing
     - Implement timeout handling for stuck processing

     3.2 Implement retry mechanisms (30 mins)

     - Add retry button for failed videos
     - Enable automatic retry for transient failures
     - Preserve original video URL for manual retry

     Total Estimated Time: 2.5-3 hours
     Priority: Critical → Major → Medium

     This plan maintains backward compatibility while solving the core video URL loss issue and modernizing the WYSIWYG video system to match our improved Markdown implementation.

⏺ Update Todos
  ⎿  ☐ Fix element replacement logic in trumbowyg.upload.js to preserve video containers
     ☐ Replace blob URL approach with VideoRenderer integration
     ☐ Test integrated video system in both WYSIWYG and Markdown modes
     ☐ Update video activation in edit-article.js for WYSIWYG videos
     ☐ Add processing state feedback and retry mechanisms