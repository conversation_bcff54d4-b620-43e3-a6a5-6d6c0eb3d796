module.exports = [
    // Facility Operations
    {
        moduleId: 'wiki',
        name: 'Knowledge Base',
        tooltip: '',
        icon: '',
        baseUIPath: '/wiki',
        group: 'Facility Operations',
    },
    {
        moduleId: 'project-management',
        name: 'Project Management',
        tooltip: '',
        icon: '',
        baseUIPath: '/project-management',
        group: 'Facility Operations',
    },
    {
        moduleId: 'designer',
        name: 'Marketing & Print',
        tooltip: '',
        icon: '',
        baseUIPath: '/designer',
        group: 'Facility Operations',
    },
    {
        moduleId: 'global-calendar',
        name: 'Global Calendar',
        tooltip: '',
        icon: '',
        baseUIPath: '/global-schedule',
        group: 'Facility Operations',
    },
    {
        moduleId: 'training-camp',
        name: 'Training Camp',
        tooltip: '',
        icon: '',
        baseUIPath: '/training-camp',
        group: 'Facility Operations',
    },
    {
        moduleId: 'free-trials',
        name: 'Free Trial Bookings',
        tooltip: '',
        icon: '',
        baseUIPath: '/workout-booking-leads',
        group: 'Facility Operations',
    },
    {
        moduleId: 'member-agreements',
        name: 'Member Agreements',
        tooltip: '',
        icon: '',
        baseUIPath: '/member-agreements',
        group: 'Facility Operations',
    },

    // Website & Digital Profile
    {
        moduleId: 'club-details',
        name: 'Club Details',
        tooltip: '',
        icon: '',
        baseUIPath: '/club-details',
        group: 'Website & Digital Profile',
    },
    {
        moduleId: 'google-analytics',
        name: 'Google Analytics',
        tooltip: '',
        icon: '',
        baseUIPath: '/club-analytics',
        group: 'Website & Digital Profile',
    },
    {
        moduleId: 'review-management',
        name: 'Review Management',
        tooltip: '',
        icon: '',
        baseUIPath: '/review-management',
        group: 'Website & Digital Profile',
    },

    // Smart Club Technology
    {
        moduleId: 'coaching-screens',
        name: 'Coaching Screens',
        tooltip: '',
        icon: '',
        baseUIPath: '/coaching-screens-ui',
        group: 'Smart Club Technology',
        prerequisites: ['device-management'],
    },
    {
        moduleId: 'cctv',
        name: 'CCTV',
        tooltip: '',
        icon: '',
        baseUIPath: '/cctv-ui',
        group: 'Smart Club Technology',
        prerequisites: ['device-management'],
    },
    {
        moduleId: 'audio-control',
        name: 'Audio Control',
        tooltip: '',
        icon: '',
        baseUIPath: '/audio-control-ui',
        group: 'Smart Club Technology',
        prerequisites: ['device-management'],
    },
    {
        moduleId: 'device-management',
        name: 'Device Management',
        tooltip: '',
        icon: '<i class="fa fa-microchip" aria-hidden="true"></i>',
        baseUIPath: '/device-management',
        group: 'Smart Club Technology',
    },

    // Member Payments & Royalties
    {
        moduleId: 'member-payments',
        name: 'Member Payments & Royalties',
        tooltip: '',
        icon: '',
        baseUIPath: '/member-payments',
        group: 'Member Payments & Royalties',
    },

    // Store
    {
        moduleId: 'store',
        name: 'Performance Hub Store',
        tooltip: '',
        icon: '',
        baseUIPath: '/store-ui',
        group: 'Performance Hub Store',
    },

    // Settings / Account
    {
        moduleId: 'business-profile',
        name: 'Business Profile (KYC)',
        tooltip: '',
        icon: '',
        baseUIPath: '/club-kyc',
        group: 'Settings / Account',
    },
    {
        moduleId: 'club-variables',
        name: 'Club Variables',
        tooltip: '',
        icon: '',
        baseUIPath: '/facility-variables',
        group: 'Settings / Account',
    },
    {
        moduleId: 'external-integrations',
        name: 'External Integrations',
        tooltip: '',
        icon: '',
        baseUIPath: '/club-integrations',
        group: 'Settings / Account',
    },
    {
        moduleId: 'agreements',
        name: 'Contracts & Agreements',
        tooltip: '',
        icon: '',
        baseUIPath: '/agreements',
        group: 'Settings / Account',
    },
    {
        moduleId: 'billing',
        name: 'SaaS Usage & Billing',
        tooltip: '',
        icon: '',
        baseUIPath: '/saas-billing',
        group: 'Settings / Account',
    },

    // Support & HQ
    {
        moduleId: 'announcements',
        name: 'Announcements',
        tooltip: '',
        icon: '',
        baseUIPath: '/announcements',
        group: 'Support & HQ',
    },
    {
        moduleId: 'support',
        name: 'Support',
        tooltip: '',
        icon: '',
        baseUIPath: '/support',
        group: 'Support & HQ',
    },

    // External Systems
    {
        moduleId: 'gymmaster-mms',
        name: 'GymMaster MMS',
        tooltip: '',
        icon: '',
        baseUIPath: '/#',
        group: 'Business & MMS',
    },
    {
        moduleId: 'gym-leads',
        name: 'Gym Leads',
        tooltip: '',
        icon: '',
        baseUIPath: 'https://app.gymleads.net/',
        group: 'Business & MMS',
    },

    // Admin Tools
    {
        moduleId: 'confluence',
        name: 'Master Franchisee Wiki',
        tooltip: '',
        icon: '',
        baseUIPath: '/confluence-ui',
        group: 'Admin Tools',
        globalAdminOnly: true,
    },
    {
        moduleId: 'program-builder',
        name: 'Program Builder',
        tooltip: '',
        icon: '',
        baseUIPath: '/program-builder-ui',
        group: 'Admin Tools',
        globalAdminOnly: true,
    },
    {
        moduleId: 'redash',
        name: 'Redash (Business Intelligence)',
        tooltip: '',
        icon: '',
        baseUIPath: '/redash-ui',
        group: 'Admin Tools',
        globalAdminOnly: true,
    },
    {
        moduleId: 'member-lead-data',
        name: 'All Member & Lead Data',
        tooltip: '',
        icon: '',
        baseUIPath: '/all-members',
        group: 'Admin Tools',
        globalAdminOnly: true,
    },
    {
        moduleId: 'file-uploader',
        name: 'File Uploader',
        tooltip: '',
        icon: '',
        baseUIPath: '/secure-uploader',
        group: 'Admin Tools',
    },
    {
        moduleId: 'bulk-sms',
        name: 'Bulk SMS',
        tooltip: '',
        icon: '',
        baseUIPath: '/bulk-sms',
        group: 'Admin Tools',
        globalAdminOnly: true,
    },
    {
        moduleId: 'hardware-tracking',
        name: 'Hardware Tracking',
        tooltip: '',
        icon: '',
        baseUIPath: '/hardware-warranties',
        group: 'Admin Tools',
        globalAdminOnly: true,
    },
    {
        moduleId: 'agreements-admin',
        name: 'Contracts & Agreements',
        tooltip: '',
        icon: '',
        baseUIPath: '/agreements-admin',
        group: 'Admin Tools',
    },

    // Sites, Content & Configuration
    {
        moduleId: 'access-control',
        name: 'Performance Hub User Access',
        tooltip: '',
        icon: '<i class="fa fa-user-plus" aria-hidden="true"></i>',
        baseUIPath: '/admin-portal',
        group: 'Sites, Content & Configuration',
    },
    {
        moduleId: 'the-academy',
        name: 'The Academy (User Configuration)',
        tooltip: '',
        icon: '<i class="fa fa-user-plus" aria-hidden="true"></i>',
        baseUIPath: '/the-academy',
        group: 'Sites, Content & Configuration',
        globalAdminOnly: true,
    },
    {
        moduleId: 'wiki-admin',
        name: 'Knowledge Base Admin',
        tooltip: '',
        icon: '',
        baseUIPath: '/wiki-admin',
        group: 'Sites, Content & Configuration',
        prerequisites: ['file-uploader'],
    },
    {
        moduleId: 'store-admin',
        name: 'Store Admin',
        tooltip: '',
        icon: '',
        baseUIPath: '/store-admin-ui',
        group: 'Sites, Content & Configuration',
    },
    {
        moduleId: 'designer-admin',
        name: 'Marketing & Print Configuration',
        icon: '',
        baseUIPath: '/designer-admin',
        group: 'Sites, Content & Configuration',
        prerequisites: ['file-uploader'],
    },
    {
        moduleId: 'announcements-admin',
        name: 'Announcements Admin',
        group: 'Sites, Content & Configuration',
    },
    {
        moduleId: 'website-management',
        name: 'Website Management',
        tooltip: '',
        icon: '',
        baseUIPath: '/website-management',
        group: 'Sites, Content & Configuration',
        globalAdminOnly: true,
    },
    {
        moduleId: 'holiday-management',
        name: 'Holiday Management',
        tooltip: '',
        icon: '',
        baseUIPath: '/holiday-management',
        group: 'Sites, Content & Configuration',
        globalAdminOnly: true,
    },
    {
        moduleId: 'kanban-settings',
        name: 'Kanban Settings',
        tooltip: '',
        icon: '',
        baseUIPath: '/kanban-settings',
        group: 'Sites, Content & Configuration',
        globalAdminOnly: true,
    },
    {
        moduleId: 'charges-config',
        name: 'Charges & Fees Configuration',
        tooltip: '',
        icon: '',
        baseUIPath: '/club-charges-and-fees',
        group: 'Sites, Content & Configuration',
        globalAdminOnly: true,
    },
    {
        moduleId: 'billing-admin',
        name: 'SaaS Billing Configuration',
        tooltip: '',
        icon: '',
        baseUIPath: '/saas-billing-admin',
        group: 'Sites, Content & Configuration',
        globalAdminOnly: true,
    },
    {
        moduleId: 'ph-config',
        name: 'Performance Hub Configuration',
        tooltip: '',
        icon: '',
        baseUIPath: '/ph-admin',
        group: 'Sites, Content & Configuration',
        globalAdminOnly: true,
    },
    {
        moduleId: 'membership-management',
        name: 'Membership Management',
        tooltip: '',
        icon: '',
        baseUIPath: '/membership-management',
        group: 'Sites, Content & Configuration',
        globalAdminOnly: true,
    },

    // External Systems
    {
        moduleId: 'ticketing-system',
        name: 'Support/Ticketing System',
        tooltip: '',
        icon: '',
        baseUIPath: 'https://ticketing.gymsystems.co/scp/',
        group: 'External Systems',
    },
    {
        moduleId: 'monitoring-page',
        name: 'Status/Monitoring Page',
        tooltip: '',
        icon: '',
        baseUIPath: 'https://status.performancehub.co/dashboard/',
        group: 'External Systems',
    },
    {
        moduleId: 'graylog',
        name: 'Graylog Infrastructure Monitoring',
        tooltip: '',
        icon: '',
        baseUIPath: 'https://graylog.aws.gymsystems.co/search',
        group: 'External Systems',
    },
    {
        moduleId: 'cronicle',
        name: 'Cronicle (Redash Scheduler)',
        tooltip: '',
        icon: '',
        baseUIPath: 'http://*************:3012/',
        group: 'External Systems',
    },
];
