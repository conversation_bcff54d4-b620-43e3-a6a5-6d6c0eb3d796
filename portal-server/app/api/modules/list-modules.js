const Router = require('express').Router;
const modules = require('../../data/modules');

/** @swagger
 * /api/modules:
 *    get:
 *      description: Returns list of all modules
 *      tags:
 *        - Modules
 *      security:
 *        - googleAppsAccount:
 *          - clubmanager
 *          - administrator
 *      produces:
 *        - application/json
 *      responses:
 *        200:
 *          description: Returns JSON response of all currencies.
 *        401:
 *          description: Not authorised to access this function. Check the user is signed in, Check the user actually has permission to access this club.
 */

module.exports = Router({ mergeParams: true }).get('/modules', async (req, res) => {
    return res
        .status(200)
        .json(modules.filter(({ globalAdminOnly }) => !globalAdminOnly));
});
