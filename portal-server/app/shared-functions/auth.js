const jwt = require('jsonwebtoken');
const _get = require('lodash/get');
const accessServices = require('../services/dynamodb/accesspermissions');
const listClubs = require('../api/clubs/listclubs');
const { get: getFacility, scan: queryFacilities } = require('../services/dynamodb/club');
const { getBrand, listBrands } = require('../services/dynamodb/brands');
const { listRolesByOrganisation, listRoles } = require('../services/dynamodb/roles');
const modules = require('../data/modules');
const { getOrganisation, listOrganisations } = require('../services/dynamodb/organisations');

async function isGlobalAdmin(email) {
    if (!email) return false;

    const userRes = await accessServices.getByEmail(email);
    if (userRes[0] !== 200) return false;
    return userRes[1].isGlobalAdmin;
}

async function hasAccessToFacility(email, facilityId) {
    if (!email || !facilityId) {
        console.error(`Access denied: Missing required parameters.`);
        return false;
    }

    const [userRes, facilityRes] = await Promise.all([
        accessServices.getByEmail(email),
        (() => facilityId && getFacility(facilityId))(),
    ]);

    if (userRes[0] !== 200) {
        console.error(`Access denied: User ${email} not found.`);
        return false;
    }

    if (facilityId && !facilityRes[0]) {
        console.error(`Access denied: Facility ${facilityId} not found.`);
        return false;
    }

    if (userRes[1].isGlobalAdmin) return true;

    const roleIds = userRes[1].roles?.map(({ roleId }) => roleId) || [];
    const [rolesRes, brandRes] = await Promise.all([listRoles({ roleId: roleIds }), getBrand(facilityRes[1].brandId)]);

    if (rolesRes[0] !== 200) {
        console.error(`Access denied: Roles not found.`);
        return false;
    }

    if (brandRes[0] !== 200) {
        console.error(`Access denied: Brand not found.`);
        return false;
    }

    const { country } = facilityRes[1]?.placeData || {};

    return rolesRes[1]?.some((role) => {
        const accessToOrg = role.organisationId === brandRes[1].organisationId;
        const userRole = userRes[1].roles?.find((userRole) => userRole.roleId === role.roleId);
        const userRoleFacilities = userRole?.facilities || [];
        const userRoleRegions = userRole?.regions || [];

        const accessToFacility =
            role.accessScope === 'facilities'
                ? role.facilities.includes(facilityId)
                : role.accessScope === 'regions'
                ? country?.iso_code && role.regions.includes(country.iso_code)
                : role.accessScope === 'template'
                ? userRoleFacilities.includes(facilityId) ||
                  (country?.iso_code && userRoleRegions.includes(country.iso_code))
                : role.accessScope === 'organisation';

        return accessToOrg && accessToFacility;
    });
}

// Accepts either facilityId or organisationId, facilityId takes precedence
async function hasModuleAccess({ email, facilityId, organisationId, moduleId }) {
    // Add/Edit agreement templates have a facilityId in the body w/c can be global
    if (typeof facilityId !== 'string' || facilityId.toLowerCase() === 'global') facilityId = null;

    if (!email || (!facilityId && !organisationId)) {
        console.error(`Access denied: Missing required parameters.`);
        return false;
    }

    const module = modules.find((m) => m.moduleId === moduleId);
    if (!module) {
        console.error(`Access denied: Module ${moduleId} not found.`);
        return false;
    }

    const dependentModules = modules.filter((m) => m.prerequisites?.includes(moduleId));

    const [userRes, facilityRes] = await Promise.all([
        accessServices.getByEmail(email),
        (() => facilityId && getFacility(facilityId))(),
    ]);

    if (userRes[0] !== 200) {
        console.error(`Access denied: User ${email} not found.`);
        return false;
    }

    if (userRes[1].isGlobalAdmin) return true;

    if (facilityId && !facilityRes[0]) {
        console.error(`Access denied: Facility ${facilityId} not found.`);
        return false;
    }
    
    if (!organisationId) {
        const brandRes = await getBrand(facilityRes[1].brandId);
        if (brandRes[0] !== 200) {
            console.error(`Access denied: Brand not found.`);
            return false;
        }

        organisationId = brandRes[1].organisationId;
    }

    const orgRes = await getOrganisation(organisationId);

    if (orgRes[0] !== 200) {
        console.error(`Access denied: Organisation ${organisationId} not found.`);
        return false;
    }

    if (module.globalAdminOnly) {
        console.error(`Access denied: Module ${moduleId} is restricted to global admins.`);
        return false;
    }

    const facility = facilityId ? facilityRes[1] : null;

    if (facility) {
        const brandsRes = await getBrand(facility.brandId);
        if (brandsRes[0] !== 200) {
            console.error(`Access denied: Brand not found.`);
            return false;
        }

        organisationId = brandsRes[1].organisationId;
    }

    const rolesRes = await listRolesByOrganisation(organisationId);
    if (rolesRes[0] !== 200) {
        console.error(`Access denied: Roles not found.`);
        return false;
    }

    const { country } = facility?.placeData || {};

    const roles = rolesRes[1].filter((role) => {
        const userIsAssignedToRole = userRes[1].roles?.some(({ roleId }) => roleId === role.roleId);
        const userRole = userRes[1].roles?.find((userRole) => userRole.roleId === role.roleId);
        const userRoleFacilities = userRole?.facilities || [];
        const userRoleRegions = userRole?.regions || [];

        const hasAccessToFacility = facility
            ? role.accessScope === 'facilities'
                ? role.facilities.includes(facilityId)
                : role.accessScope === 'regions'
                ? country?.iso_code && role.regions.includes(country.iso_code)
                : role.accessScope === 'template'
                ? userRoleFacilities.includes(facilityId) ||
                  (country?.iso_code && userRoleRegions.includes(country.iso_code))
                : role.accessScope === 'organisation'
            : true;

        const roleModules = role.modules
            ?.filter((modId) => orgRes[1]?.modules?.some((mod) => mod === modId)) || [];

        let hasAccessToModule = role.moduleMode === 'manual'
            ? roleModules?.includes(moduleId)
            : !roleModules?.includes(moduleId);

        // Check if this module has dependents that the user has access to
        if (!hasAccessToModule) {
            hasAccessToModule = dependentModules.some((dependentModule) => {
                return role.moduleMode === 'manual'
                    ? roleModules?.includes(dependentModule.moduleId)
                    : !roleModules?.includes(dependentModule.moduleId);
            });
        }

        return userIsAssignedToRole && hasAccessToFacility && hasAccessToModule;
    });

    if (!roles.length) {
        console.error(
            `Access denied: User does not have the required role for module ${moduleId} of facility ${facilityId}.`
        );
        return false;
    }

    return true;
}

async function getUserOrgAccessForModule(email, moduleId) {
    const access = { email, isGlobalAdmin: false, orgs: [], allRegions: [], allFacilities: [] };

    if (!email) return access;

    const moduleData = modules.find((m) => m.moduleId === moduleId);
    if (!moduleData) {
        console.error(`Access denied: Module ${moduleId} not found.`);
        return access;
    }

    const dependentModules = modules.filter((m) => m.prerequisites?.includes(moduleId));

    const [userRes, orgsRes] = await Promise.all([
        accessServices.getByEmail(email),
        listOrganisations(),
    ]);

    if (userRes[0] !== 200) return access;
    if (orgsRes[0] !== 200) return access;
    let orgsList = orgsRes[1];

    let brandsQuery = null;
    let roles = [];

    if (!userRes[1].isGlobalAdmin) {
        if (!userRes[1].roles?.length) return access;

        orgsList = orgsList.filter(
            (org) => org.modules?.some((modId) => modId === moduleId)
        );

        const userRoleIds = userRes[1].roles.map(({ roleId }) => roleId);
        const userRoles = await listRoles({ roleId: userRoleIds });
        if (userRoles[0] !== 200) return access;

        roles = userRoles[1].filter((role) => {
            const org = orgsList.find((org) => org.organisationId === role.organisationId);
            const roleModules = role.modules?.filter((moduleId) => org?.modules?.some((modId) => modId === moduleId)) || [];

            const hasAccessToModule = role.moduleMode === 'manual'
                ? roleModules.includes(moduleId)
                : !roleModules.includes(moduleId);

            if (hasAccessToModule) return true;

            // Check if this module has dependents that the user has access to
            return dependentModules.some((dependentModule) => {
                return role.moduleMode === 'manual'
                    ? roleModules.includes(dependentModule.moduleId)
                    : !roleModules.includes(dependentModule.moduleId);
            });
        });

        const orgIds = Array.from(new Set(roles.map((role) => role.organisationId)));
        orgsList = orgsList.filter((org) => orgIds.includes(org.organisationId));
        brandsQuery = { organisationId: orgIds };

        if (orgIds.length < 1) return access;
    } else {
        access.isGlobalAdmin = true;
    }

    const brandsRes = await listBrands(brandsQuery);
    if (brandsRes[0] !== 200) return access;
    const brandIds = brandsRes[1].map((b) => b.brandId);

    const facilityRes = await queryFacilities({ brandId: brandIds });
    if (!facilityRes[0]) return access;

    access.orgs = orgsList
        .map((org) => {
            const brands = brandsRes[1].filter((b) => b.organisationId === org.organisationId).map((b) => b.brandId);

            const orgFacilities = facilityRes[1]
                .filter(({ brandId, id: facilityId, placeData }) => {
                    const facilityBelongsToOrg = brands.includes(brandId);

                    const countryIso = placeData?.country?.iso_code;
                    const orgRoles = roles.filter((role) => role.organisationId === org.organisationId);
                    const facilityInRoles = orgRoles.some((role) => {
                        const userRole = userRes[1].roles?.find((userRole) => userRole.roleId === role.roleId);
                        const userRoleFacilities = userRole?.facilities || [];
                        const userRoleRegions = userRole?.regions || [];

                        return role.accessScope === 'regions'
                            ? role.regions.includes(countryIso)
                            : role.accessScope === 'facilities'
                            ? role.facilities.includes(facilityId)
                            : role.accessScope === 'template'
                            ? userRoleFacilities.includes(facilityId) || userRoleRegions.includes(countryIso)
                            : role.accessScope === 'organisation';
                    });

                    return facilityBelongsToOrg && (access.isGlobalAdmin || facilityInRoles);
                })
                .sort((a, b) => (a.name || a.id).localeCompare(b.name || b.id));

            const facilities = orgFacilities.map((facility) => {
                const countryIso = facility.placeData?.country?.iso_code;
                const brand = brandsRes[1].find((b) => b.brandId === facility.brandId);

                const obj = {
                    id: facility.id,
                    name: facility.name || facility.id,
                    brand: brand ? { brandId: brand.brandId, name: brand.name } : null,
                    countryIso,
                };

                if (!access.allFacilities.some((f) => f.id === facility.id)) {
                    access.allFacilities.push(obj);
                }

                return obj;
            });

            const regions = orgFacilities
                .reduce((acc, facility) => {
                    const { iso_code, full_name } = facility.placeData?.country || {};
                    if (!iso_code) return acc;
                    if (acc.some((region) => region.iso_code === iso_code)) return acc;
                    acc.push({ iso_code, full_name });

                    if (!access.allRegions.some((region) => region.iso_code === iso_code)) {
                        access.allRegions.push({ iso_code, full_name });
                    }

                    return acc;
                }, [])
                .sort((a, b) => a.full_name.localeCompare(b.full_name));

            return {
                organisationId: org.organisationId,
                name: org.name,
                facilities,
                regions,
            };
        })
        .sort((a, b) => a.name.localeCompare(b.name));

    access.allFacilities = access.allFacilities.sort((a, b) => (a.name || a.id).localeCompare(b.name || b.id));
    access.allRegions = access.allRegions.sort((a, b) => a.full_name.localeCompare(b.full_name));

    return access;
}

function getAuthedEmail(req) {
    const { 'x-forwarded-email': email, 'x-forwarded-user': user } = req.headers;
    return email || user || false;
}

function hasEmailHeader(req) {
    return !!getAuthedEmail(req);
}

function getOrganisationIdFromParams(req) {
    if (!req || typeof req !== 'object') return false;

    return (
        req.params?.organisationId ||
        req.params?.organisationid ||
        req.params?.organisation ||
        req.params?.organisationID ||
        req.query?.organisationId ||
        req.query?.organisationid ||
        req.query?.organisation ||
        req.query?.organisationID ||
        req.body?.organisationId ||
        req.body?.organisationid ||
        req.body?.organisation ||
        req.body?.organisationID
    );
}

function getClubIDFromParams(req) {
    if (!req || typeof req !== 'object') return false;

    // Check in request parameters first
    const clubID =
        _get(req, 'params.club') ||
        _get(req, 'params.clubID') ||
        _get(req, 'params.clubId') ||
        _get(req, 'params.clubid') ||
        _get(req, 'params.facilityID') ||
        _get(req, 'params.facilityid') ||
        _get(req, 'params.facility');

    if (clubID) return clubID;

    // Check in request body if it exists and is an object
    if (req.body && typeof req.body === 'object') {
        return (
            _get(req.body, 'club') ||
            _get(req.body, 'clubID') ||
            _get(req.body, 'clubId') ||
            _get(req.body, 'clubid') ||
            _get(req.body, 'facilityID') ||
            _get(req.body, 'facilityid') ||
            _get(req.body, 'facility') ||
            false
        );
    }

    return false;
}

function authoriseGuest(req, res, next) {
    const token = req.cookies.auth_token;

    if (!token) {
        return res.status(401).json({ message: 'Unauthorised. Access token is required.' });
    }

    const secretKey = process.env.PH_TEMP_ACCESS_SECRET_KEY;

    if (!secretKey) {
        throw new Error('Server misconfiguration.');
    }

    try {
        // Verify the token using the secret key
        const decoded = jwt.verify(token, secretKey);

        // Check for expected properties in the payload
        if (!decoded.clubs || !decoded.service || !decoded.iss) {
            return res.status(400).json({ message: 'Invalid token structure' });
        }

        const requestedClub = getClubIDFromParams(req);
        if (requestedClub) {
            if (!decoded.clubs || !decoded.clubs.length) {
                return res.status(400).json({ message: 'Unauthorised' });
            }

            if (!decoded.clubs.includes(requestedClub)) {
                return res.status(400).json({ message: 'Unauthorised' });
            }
        }

        // Check if the token has expired
        const currentTime = Math.floor(Date.now() / 1000); // Current time in seconds
        if (decoded.exp < currentTime) {
            return res.status(401).json({ message: 'Token has expired' });
        }

        // Restriction for issuer..
        // const knownSources = ['uapi', 'ubxtraining.com'];
        // if (decoded.iss !== 'uapi') {
        //     return res.status(401).json({ message: 'Invalid token source' });
        // }

        // TODO: Create special handling for "service", currently, it will just be ignored
        // Use-case: cctv only type of access

        return next();
    } catch (error) {
        // Token verification failed
        return res.status(401).json({ message: 'Invalid token' });
    }
}

module.exports = {
    hasModuleAccess,
    isGlobalAdmin,
    getFacilityIdFromParams: getClubIDFromParams,
    hasAccessToFacility,
    getOrganisationIdFromParams,
    checkGlobalAdminAccess: async (req, res, next) => {
        const email = getAuthedEmail(req);
        const isAdmin = await isGlobalAdmin(email);
        if (isAdmin) return next();
        return res.status(401).send({ message: 'You are not authorized to access this resource' });
    },
    checkModuleAccess: (moduleId) => async (req, res, next) => {
        const email = getAuthedEmail(req);
        const facilityId = getClubIDFromParams(req);
        const organisationId = getOrganisationIdFromParams(req);
        const hasAccess = await hasModuleAccess({ email, facilityId, moduleId, organisationId });
        if (hasAccess) return next();

        return res.status(401).send({ message: 'You are not authorized to access this resource' });
    },
    checkFacilityAccess: async (req, res, next) => {
        const email = getAuthedEmail(req);
        const facilityId = getClubIDFromParams(req);
        const hasAccess = await hasAccessToFacility(email, facilityId);
        if (hasAccess) return next();

        return res.status(401).send({ message: 'You are not authorized to access this resource' });
    },
    getUserOrgAccessForModule,
    loggedUserAccount: function (req) {
        return getAuthedEmail(req);
    },
    // Check if we have been authenticated from google (no permissions - just if we're authenticated or not)
    hasEmailHeader: function (req, res, callback) {
        if (hasEmailHeader(req)) {
            callback(true);
        } else {
            callback(false);
        }
    },
    listClubRegion: function (clubID, callback) {
        listClubs.returnClubs(false, function (clubs) {
            let matchedClub = false;

            for (const club of clubs) {
                if (club.id == clubID) {
                    matchedClub = true;
                    callback(
                        club.localisation !== undefined && club.localisation.country !== undefined
                            ? club.localisation.country
                            : false
                    );
                }
            }

            // There was no match for this clubID.
            if (!matchedClub) callback(false);
        });
    },
    listAuthorisedClubs: async (email) => {
        const access = { isGlobalAdmin: false, signedin: email, clubs: [], organisations: [] };

        try {
            const userRes = await accessServices.getByEmail(email);
            if (userRes[0] !== 200) return access;

            access.signedin = userRes[1].email;

            if (userRes[1].isGlobalAdmin) {
                const [facilities, brands, organisations] = await Promise.all([queryFacilities(), listBrands(), listOrganisations()]);

                if (!facilities[0]) return access;
                if (brands[0] !== 200) return access;
                if (organisations[0] !== 200) return access;

                access.isGlobalAdmin = true;
                access.clubs = facilities[1].map((facility) => {
                    const brand = brands[1].find((b) => b.brandId === facility.brandId);
                    const org = organisations[1].find((o) => o.organisationId === brand?.organisationId);
                    const orgModules = modules.filter((module) => org?.modules?.includes(module.moduleId) && !module.globalAdminOnly);
                    return { ...facility, modules: orgModules, isOrganisationAdmin: true };
                });
            } else {
                const roleIds = userRes[1].roles?.map(({ roleId }) => roleId) || [];
                const rolesRes = await listRoles({ roleId: roleIds });
                if (rolesRes[0] !== 200) return access;

                const orgIds = Array.from(new Set(rolesRes[1].map((role) => role.organisationId)));
                const [brandsRes, orgsRes] = await Promise.all([
                    listBrands({ organisationId: orgIds }),
                    listOrganisations({ organisationId: orgIds }),
                ]);
                
                if (brandsRes[0] !== 200) return access;
                if (orgsRes[0] !== 200) return access;

                const roleWithBrands = rolesRes[1].map((role) => {
                    const brands = brandsRes[1].filter((b) => b.organisationId === role.organisationId);
                    return { ...role, brands };
                });

                const roleFacilities = await Promise.all(
                    roleWithBrands.map(async (role) => {
                        const orgFacilities = await queryFacilities({ brandId: role.brands.map((b) => b.brandId) });
                        if (!orgFacilities[0]) return [];

                        const org = orgsRes[1].find((o) => o.organisationId === role.organisationId);
                        const orgModules = modules.filter((mod) => org?.modules?.includes(mod.moduleId) && !mod.globalAdminOnly);
                        const isOrganisationAdmin = role.isOrganisationAdmin || false;
                        
                        let roleModules = orgModules.filter((module) => role.modules?.includes(module.moduleId));
                        if (role.moduleMode === 'allowAllExcept') {
                            roleModules = orgModules.filter((module) => !role.modules?.includes(module.moduleId));
                        }

                        // Include prerequisite modules
                        const prerequisiteIds = roleModules.flatMap((module) => module.prerequisites || []);
                        const prerequisiteModules = modules.filter((mod) => prerequisiteIds.includes(mod.moduleId));

                        // Remove duplicates based on moduleId
                        roleModules = [...roleModules, ...prerequisiteModules].filter(
                            (mod, index, self) => index === self.findIndex((m) => m.moduleId === mod.moduleId)
                        );

                        let roleFacilities = [];
                        if (role.accessScope === 'facilities') {
                            roleFacilities = orgFacilities[1].filter((facility) =>
                                role.facilities.includes(facility.id)
                            );
                        } else if (role.accessScope === 'regions') {
                            roleFacilities = orgFacilities[1].filter((facility) => {
                                const countryIso = facility.placeData?.country?.iso_code;
                                return role.regions.includes(countryIso);
                            });
                        } else if (role.accessScope === 'organisation') {
                            roleFacilities = orgFacilities[1];
                        } else if (role.accessScope === 'template') {
                            const userRole = userRes[1].roles?.find((userRole) => userRole.roleId === role.roleId);
                            const userRoleFacilities = userRole?.facilities || [];
                            const userRoleRegions = userRole?.regions || [];

                            roleFacilities = orgFacilities[1].filter(
                                (facility) =>
                                    userRoleFacilities.includes(facility.id) ||
                                    userRoleRegions.includes(facility.placeData?.country?.iso_code)
                            );
                        }

                        // Attach modules to the facilities
                        return roleFacilities.map((facility) => {
                            return { ...facility, modules: roleModules, isOrganisationAdmin };
                        });
                    })
                );

                access.clubs = roleFacilities.flat().reduce((acc, facility) => {
                    const index = acc.findIndex(({ id }) => id === facility.id);

                    // if already added, just merge the modules and use the correct isOrganisationAdmin value
                    if (index > -1) {
                        acc[index].isOrganisationAdmin = facility.isOrganisationAdmin || acc[index].isOrganisationAdmin;

                        acc[index].modules = [...(acc[index].modules || []), ...(facility.modules || [])]
                            .filter((mod, index, self) => {
                                return index === self.findIndex((m) => m.moduleId === mod.moduleId);
                            });

                        return acc;
                    }

                    return [...acc, facility];
                }, []);
            }

            access.clubs = await Promise.all(
                access.clubs.map(async ({ modules, isOrganisationAdmin, ...facility }) => {
                    const formatted = await listClubs.formatClubList(false, facility);
                    return { ...formatted, isOrganisationAdmin, modules };
                })
            );

            const userOrgs = access.clubs
                .reduce((acc, facility) => {
                    const { organisationId, organisation, organisationLogo, isOrganisationAdmin } = facility;
                    if (!organisationId || !organisation) return acc;

                    const org = acc[organisationId] || {
                        organisationId,
                        name: organisation,
                        logo: organisationLogo,
                        isOrganisationAdmin: isOrganisationAdmin || false,
                    };

                    org.isOrganisationAdmin = org.isOrganisationAdmin || isOrganisationAdmin;
                    acc[organisationId] = org;
                    return acc;
                }, {});

            access.organisations = Object.values(userOrgs)
                .sort((a, b) => (a.name || '').localeCompare(b.name || ''));

            return access;
        } catch (error) {
            console.error('Failed to get user access', error);
            return access;
        }
    },
    authoriseGuest,
};
