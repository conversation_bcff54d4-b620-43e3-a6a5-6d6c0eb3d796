/**
 * Streaming Video Rendering Module
 * Handles JSON metadata and constructs proper streaming URLs for HLS/DASH playback
 */

/**
 * Configuration class for VideoRenderer
 * Centralizes all constants, delays, and configuration options
 */
class VideoRendererConfig {
    static DELAYS = {
        STAGGER: 100,
        RETRY: 200,
        PROCESSING_RETRY: 60000,
        DEBOUNCE: 800
    };
    
    static FILE_EXTENSIONS = {
        VIDEO: ['avi', 'mkv', 'mp4', 'webm', 'mov', 'mpeg'],
        AUDIO: ['mp3', 'wav', 'aac', 'ogg', 'm4a'],
        IMAGE: ['bmp', 'gif', 'jpg', 'jpeg', 'png', 'webp', 'svg'],
        ALL_VIDEO: ['mp4', 'mkv', 'webm', 'avi', 'mpeg', 'mov', 'mp3']  // Combined for backward compatibility
    };
    
    static MEDIA_STYLES = {
        VIDEO: {
            width: '100%',
            maxWidth: '800px',
            margin: '1rem 0',
            display: 'block'
        },
        AUDIO: {
            width: '100%',
            maxWidth: '400px',
            margin: '1rem 0',
            display: 'block'
        }
    };
    
    static SELECTORS = {
        CONTAINER: ['.video-embed-container'],
        FILE_PROXY: ['.file-to-proxy'],
        UPLOADED_MEDIA: ['.uploaded-media'],
        TARGET_CONTAINERS: ['body']
    };
    
    static PLYR_OPTIONS = {
        controls: ['play-large', 'play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
        quality: {
            default: 720,
            options: [1080, 720, 480, 360],
            forced: true,
            onChange: null
        }
    };
    
    static CONTENT_TYPES = {
        VIDEO: ['video/'],
        AUDIO: ['audio/'],
        STREAMING: ['application/dash+xml', 'application/vnd.apple.mpegurl', 'application/octet-stream']
    };
    
    static STATUS_CODES = {
        PROCESSING: ['to process', 'processing'],
        READY: ['uploaded', 'ready', 'complete'],
        ERROR: ['error']
    };
    
    static ERROR_TYPES = {
        1: 'MEDIA_ERR_ABORTED',
        2: 'MEDIA_ERR_NETWORK', 
        3: 'MEDIA_ERR_DECODE',
        4: 'MEDIA_ERR_SRC_NOT_SUPPORTED'
    };
    
    static READY_STATES = {
        0: 'HAVE_NOTHING',
        1: 'HAVE_METADATA',
        2: 'HAVE_CURRENT_DATA',
        3: 'HAVE_FUTURE_DATA',
        4: 'HAVE_ENOUGH_DATA'
    };
    
    static NETWORK_STATES = {
        0: 'NETWORK_EMPTY',
        1: 'NETWORK_IDLE',
        2: 'NETWORK_LOADING',
        3: 'NETWORK_NO_SOURCE'
    };
}

/**
 * URL Constructor for Streaming Videos
 * Handles complex URL construction logic with fallback patterns
 */
class StreamingUrlConstructor {
    constructor(metadata, originalUrl, config = VideoRendererConfig) {
        this.metadata = metadata;
        this.originalUrl = originalUrl;
        this.config = config;
        this.organisationId = this._extractOrganisationId();
    }
    
    constructUrl() {
        this._validateMetadata();
        const correctS3Key = this._processS3Key();
        const queryString = this._buildQueryString();
        
        return this._buildUrl(correctS3Key, queryString);
    }
    
    _validateMetadata() {
        const { bucket, s3Key, region } = this.metadata;
        if (!bucket || !s3Key || !region) {
            throw new Error('Missing required metadata fields for streaming URL construction');
        }
    }
    
    _extractOrganisationId() {
        // Try to extract from URL first
        const fromUrl = this._extractFromUrl();
        if (fromUrl) return fromUrl;
        
        // Try from metadata
        const fromMetadata = this._extractFromMetadata();
        if (fromMetadata) return fromMetadata;
        
        // Try from global session
        return this._extractFromSession();
    }
    
    _extractFromUrl() {
        if (this.originalUrl && this.originalUrl.includes('organisationId=')) {
            const match = this.originalUrl.match(/organisationId=([^&]+)/);
            return match ? match[1] : null;
        }
        return null;
    }
    
    _extractFromMetadata() {
        return this.metadata && this.metadata.organisationId ? this.metadata.organisationId : null;
    }
    
    _extractFromSession() {
        return (typeof USER_SESSION !== 'undefined' && USER_SESSION.organisationId) 
            ? USER_SESSION.organisationId : null;
    }
    
    _processS3Key() {
        const { s3Key } = this.metadata;
        
        // Handle both correct type prefix and incorrect "file/" prefix
        // Extract the filename and detect proper type
        const [currentType, filename] = s3Key.includes('/') ? s3Key.split('/', 2) : ['file', s3Key];
        const detectedType = this._getFileType(filename);
        
        return `${detectedType}/${filename}`;
    }
    
    _getFileType(filename) {
        const extension = filename.split('.').pop().toLowerCase();

        if (this.config.FILE_EXTENSIONS.VIDEO.includes(extension)) {
            return 'video';
        } else if (this.config.FILE_EXTENSIONS.AUDIO.includes(extension)) {
            return 'audio';
        } else if (this.config.FILE_EXTENSIONS.IMAGE.includes(extension)) {
            return 'image';
        } else {
            return 'file';
        }
    }
    
    _buildQueryString() {
        return this.organisationId ? `?organisationId=${this.organisationId}` : '';
    }
    
    _buildUrl(s3Key, queryString) {
        // Preserve the original URL pattern from the originalUrl
        const hasApiPrefix = this.originalUrl && this.originalUrl.includes('/api/admin/secure-upload/');
        const isPrivate = this.originalUrl && this.originalUrl.includes('/private/');
        
        const prefix = hasApiPrefix ? '/api/admin/secure-upload' : '/admin/secure-upload';
        const visibility = isPrivate ? 'private' : 'public';
        
        return `${prefix}/${visibility}/${s3Key}${queryString}`;
    }
}

/**
 * Element Processor for Video Containers
 * Consolidates duplicate processing logic across different element types
 */
class ElementProcessor {
    constructor(renderer, config) {
        this.renderer = renderer;
        this.config = config;
    }
    
    processElements(containers, processingSpec) {
        const elements = containers.find(processingSpec.selectors.join(', '));
        
        if (elements.length === 0) {
            return;
        }
        
        elements.each((index, element) => {
            const delay = processingSpec.baseDelay + (index * this.config.DELAYS.STAGGER);
            processingSpec.handler.call(this.renderer, $(element), delay);
        });
    }
    
    processVideoContainers(containers, config) {
        this.processElements(containers, {
            selectors: config.containerSelectors,
            baseDelay: 0,
            handler: this.renderer._processVideoContainer
        });
    }
    
    processFileProxyElements(containers, config) {
        this.processElements(containers, {
            selectors: config.fileProxySelectors,
            baseDelay: config.retryDelay,
            handler: this.renderer._convertFileProxyToVideoContainer
        });
    }
    
    processUploadedMediaElements(containers, config) {
        this.processElements(containers, {
            selectors: ['.uploaded-media'],
            baseDelay: config.retryDelay,
            handler: this.renderer._processUploadedMediaElement
        });
    }
    

}

/**
 * Media Player Factory
 * Creates appropriate media players (HLS/DASH/Native) with strategy pattern
 */
class MediaPlayerFactory {
    constructor(config, debugLogger) {
        this.config = config;
        this.debugLogger = debugLogger;
    }
    
    createPlayer(streamingUrl, mediaElement) {
        const strategy = this._getPlayerStrategy(streamingUrl);
        return strategy.initialize(mediaElement, streamingUrl);
    }
    
    _getPlayerStrategy(streamingUrl) {
        // Detect content type from URL
        if (streamingUrl.includes('.m3u8') || streamingUrl.includes('mpegurl')) {
            return new HLSPlayerStrategy(this.config, this.debugLogger);
        } else if (streamingUrl.includes('.mpd') || streamingUrl.includes('dash')) {
            return new DASHPlayerStrategy(this.config, this.debugLogger);
        } else if (streamingUrl.includes('/api/admin/stream/') || streamingUrl.endsWith('.mov')) {
            return new NativePlayerStrategy(this.config, this.debugLogger);
        } else {
            return new AutoDetectPlayerStrategy(this.config, this.debugLogger);
        }
    }
}

/**
 * Base Player Strategy
 */
class PlayerStrategy {
    constructor(config, debugLogger) {
        this.config = config;
        this.debugLogger = debugLogger;
    }
    
    initialize(mediaElement, streamingUrl) {
        const mediaEl = mediaElement[0];
        
        if (this._isPlyrAvailable()) {
            return this._initializeWithPlyr(mediaElement, streamingUrl);
        } else {
            return this._initializeDirect(mediaEl, streamingUrl);
        }
    }
    
    _isPlyrAvailable() {
        return typeof window.Plyr === 'function';
    }
    
    _getPlyrOptions() {
        if (window.defaultPlyrOptions) {
            return { ...window.defaultPlyrOptions };
        }
        return { ...this.config.PLYR_OPTIONS };
    }
    
    _storeReferences(mediaElement, player, plyrPlayer = null) {
        if (player) mediaElement.data(this._getPlayerDataKey(), player);
        if (plyrPlayer) mediaElement.data('plyr', plyrPlayer);
    }
    
    _getPlayerDataKey() {
        return 'player'; // Override in subclasses
    }
}

/**
 * HLS Player Strategy
 */
class HLSPlayerStrategy extends PlayerStrategy {
    _initializeWithPlyr(mediaElement, streamingUrl) {
        const hls = new window.Hls();
        const plyrOptions = this._getPlyrOptions();
        
        plyrOptions.quality.onChange = function (quality) {
            hls.levels.forEach((level, levelIndex) => {
                if (level.height !== quality) return;
                hls.currentLevel = levelIndex;
            });
        };
        
        const player = new window.Plyr(mediaElement[0], plyrOptions);
        
        player.on('play', () => {
            if (!hls.media) {
                hls.loadSource(streamingUrl);
                hls.attachMedia(mediaElement[0]);
            }
        });
        
        this._storeReferences(mediaElement, hls, player);
        this.debugLogger.streaming('HLS player with Plyr initialized');
        return player;
    }
    
    _initializeDirect(mediaEl, streamingUrl) {
        const hls = new window.Hls();
        hls.loadSource(streamingUrl);
        hls.attachMedia(mediaEl);
        this.debugLogger.streaming('Direct HLS player initialized');
        return hls;
    }
    
    _getPlayerDataKey() {
        return 'hls';
    }
}

/**
 * DASH Player Strategy
 */
class DASHPlayerStrategy extends PlayerStrategy {
    _initializeWithPlyr(mediaElement, streamingUrl) {
        const dash = window.dashjs.MediaPlayer().create();
        const plyrOptions = this._getPlyrOptions();
        
        plyrOptions.quality.onChange = function (quality) {
            const qualityIndex = plyrOptions.quality.options.findIndex((q) => q === quality);
            dash.setQualityFor("video", qualityIndex);
        };
        
        const player = new window.Plyr(mediaElement[0], plyrOptions);
        
        player.on('play', () => {
            if (!dash.isReady()) {
                dash.initialize(mediaElement[0], streamingUrl, false);
            }
        });
        
        this._storeReferences(mediaElement, dash, player);
        this.debugLogger.streaming('DASH player with Plyr initialized');
        return player;
    }
    
    _initializeDirect(mediaEl, streamingUrl) {
        const dash = window.dashjs.MediaPlayer().create();
        dash.initialize(mediaEl, streamingUrl, false);
        this.debugLogger.streaming('Direct DASH player initialized');
        return dash;
    }
    
    _getPlayerDataKey() {
        return 'dash';
    }
}

/**
 * Native Player Strategy (for .mov files and internal streaming)
 */
class NativePlayerStrategy extends PlayerStrategy {
    _initializeWithPlyr(mediaElement, streamingUrl) {
        mediaElement[0].src = streamingUrl;
        
        try {
            const plyrOptions = this._getPlyrOptions();
            const player = new window.Plyr(mediaElement[0], plyrOptions);
            this._storeReferences(mediaElement, null, player);
            this.debugLogger.streaming('Native player with Plyr initialized');
            return player;
        } catch (error) {
            this.debugLogger.warn('Plyr initialization failed, using basic controls', { error: error.message });
            return this._initializeDirect(mediaElement[0], streamingUrl);
        }
    }
    
    _initializeDirect(mediaEl, streamingUrl) {
        mediaEl.src = streamingUrl;
        this.debugLogger.streaming('Direct native player initialized');
        return mediaEl;
    }
}

/**
 * Auto-Detect Player Strategy (detects from response headers)
 */
class AutoDetectPlayerStrategy extends PlayerStrategy {
    async initialize(mediaElement, streamingUrl) {
        try {
            const contentType = await this._detectContentType(streamingUrl);
            const strategy = this._getStrategyFromContentType(contentType);
            return strategy.initialize(mediaElement, streamingUrl);
        } catch (error) {
            this.debugLogger.error('Failed to detect content type, using native fallback', { error: error.message });
            const nativeStrategy = new NativePlayerStrategy(this.config, this.debugLogger);
            return nativeStrategy.initialize(mediaElement, streamingUrl);
        }
    }
    
    async _detectContentType(streamingUrl) {
        const response = await fetch(streamingUrl, {
            method: 'HEAD',
            credentials: 'include'
        });
        return response.headers.get('content-type');
    }
    
    _getStrategyFromContentType(contentType) {
        if (contentType && contentType.includes('mpegurl')) {
            return new HLSPlayerStrategy(this.config, this.debugLogger);
        } else if (contentType && contentType.includes('dash')) {
            return new DASHPlayerStrategy(this.config, this.debugLogger);
        } else {
            return new NativePlayerStrategy(this.config, this.debugLogger);
        }
    }
}

// Global video tracking - shared across all contexts
window.processedVideos = window.processedVideos || new Set();
window.processingVideos = window.processingVideos || new Set();

// Enhanced debug logging utility
const VideoDebugLogger = {
    enabled: true,
    prefix: '[StreamingVideoRenderer]',

    log: function(level, message, data = null) {
        if (!this.enabled) return;

        const timestamp = new Date().toISOString();
        const logMessage = `${this.prefix} [${timestamp}] ${level}: ${message}`;

        if (data) {
            console.log(logMessage, data);
        } else {
            console.log(logMessage);
        }
    },

    info: function(message, data) { this.log('INFO', message, data); },
    warn: function(message, data) { this.log('WARN', message, data); },
    error: function(message, data) { this.log('ERROR', message, data); },
    debug: function(message, data) { this.log('DEBUG', message, data); },
    network: function(message, data) { this.log('NETWORK', message, data); },
    streaming: function(message, data) { this.log('STREAMING', message, data); },
    playback: function(message, data) { this.log('PLAYBACK', message, data); },
    hls: function(message, data) { this.log('HLS', message, data); },
    dash: function(message, data) { this.log('DASH', message, data); },
    success: function(message, data) { this.log('SUCCESS', message, data); }
};

/**
 * StreamingVideoRenderer class - uses HLS/DASH streaming instead of blob approach
 */
class StreamingVideoRenderer {
    constructor(options = {}) {
        this.processedVideos = window.processedVideos;
        this.processingVideos = window.processingVideos;
        this.debugLogger = VideoDebugLogger;
        this.config = VideoRendererConfig;
        
        this.defaultOptions = {
            containerSelectors: this.config.SELECTORS.CONTAINER,
            fileProxySelectors: this.config.SELECTORS.FILE_PROXY,
            targetContainers: this.config.SELECTORS.TARGET_CONTAINERS,
            staggerDelay: this.config.DELAYS.STAGGER,
            retryDelay: this.config.DELAYS.RETRY,
            maxRetries: 3,
            ...options
        };
        
        // Initialize helper classes
        this.elementProcessor = new ElementProcessor(this, this.config);
        this.mediaPlayerFactory = new MediaPlayerFactory(this.config, this.debugLogger);
    }

    /**
     * Main activation function
     */
    activateVideoPlayers(options = {}) {
        const config = { ...this.defaultOptions, ...options };
        const containers = this._getTargetContainers(config.targetContainers);
        
        if (containers.length === 0) {
            console.warn('🎥 VideoRenderer: No target containers found for video activation');
            this.debugLogger.warn('No target containers found for video activation');
            return;
        }

        // Process existing video-embed-container elements
        this.elementProcessor.processVideoContainers(containers, config);

        // Process file-to-proxy elements and convert them
        this.elementProcessor.processFileProxyElements(containers, config);

        // Process uploaded-media containers with video img tags
        this.elementProcessor.processUploadedMediaElements(containers, config);

        console.log('🎥 VideoRenderer: activateVideoPlayers completed');
    }

    /**
     * Enhanced URL processing with metadata handling
     */
    _processVideoURL(url) {
        let processedUrl = url;

        // Check if it's a secure upload URL and remove /private prefix
        if (url.includes('secure-upload') && url.includes('/private')) {
            processedUrl = url.replace('/private', '');
        }
        
        return {
            processedUrl,
            analysis: { requiresMetadata: url.includes('secure-upload') }
        };
    }

    /**
     * Enhanced video loading with streaming support
     */
    async _loadVideo(container, url) {
        const currentPlaceholder = container.find('.video-loading-placeholder');
        if (currentPlaceholder.length === 0) {
            this.debugLogger.warn('No placeholder found for video loading', { url });
            return;
        }
        
        // Process URL for security and debugging
        const { processedUrl, analysis } = this._processVideoURL(url);
        
        const loadingMsg = $('<div>', {
            html: 'Loading video...',
            'data-video-loading': 'true',
            css: {
                padding: '1rem',
                color: '#666',
                textAlign: 'center'
            }
        });
        currentPlaceholder.replaceWith(loadingMsg);
        
        try {
            if (analysis.requiresMetadata) {
                // Handle secure uploads with metadata
                await this._loadSecureVideo(container, loadingMsg, url, processedUrl);
            } else {
                // Handle direct video URLs - no metadata needed
                await this._createStreamingPlayer(container, loadingMsg, url, url, null);
            }
        } catch (error) {
            this.debugLogger.error('Video loading failed', {
                error: error.message,
                url: url,
                processedUrl: processedUrl
            });
            
            this._showError(loadingMsg, error.message);
            
            // Remove from processing on error
            if (url) {
                this.processingVideos.delete(url);
            }
        }
    }

    /**
     * Load secure upload videos by getting metadata first
     */
    async _loadSecureVideo(container, loadingMsg, originalUrl, metadataUrl) {
        // Set up processing timeout (30 seconds)
        const timeoutId = setTimeout(() => {
            this._handleProcessingTimeout(container, loadingMsg, originalUrl);
        }, 30000);

        
        try {
            // Step 1: Get metadata

            
            const response = await fetch(metadataUrl, { 
                method: 'GET', 
                credentials: 'include'
            });
            

            
            if (!response.ok) {
                const errorText = await response.text();
                this.debugLogger.error('Metadata fetch failed', {
                    status: response.status,
                    statusText: response.statusText,
                    errorText: errorText
                });
                throw new Error(`Metadata fetch failed: HTTP ${response.status} - ${errorText}`);
            }
            
            const metadata = await response.json();

            

            
            // Step 2: Check processing status
            const statusCheck = this._checkProcessingStatus(metadata);
            if (!statusCheck.canPlay) {
                this._showProcessingStatus(loadingMsg, statusCheck);
                
                if (statusCheck.shouldRetry) {
                    // Retry after delay for processing videos
                    setTimeout(() => {
                        this._loadVideo(container, originalUrl);
                    }, this.config.DELAYS.PROCESSING_RETRY);
                }
                return;
            }
            
            // Step 3: Construct streaming URL from metadata
            const streamingUrl = this._constructStreamingURL(metadata, originalUrl);

            
            // Step 4: Create streaming player
            await this._createStreamingPlayer(container, loadingMsg, originalUrl, streamingUrl, metadata);
            
            // Clear timeout on success
            clearTimeout(timeoutId);
            
        } catch (error) {
            // Clear timeout on error
            clearTimeout(timeoutId);
            throw new Error(`Secure video loading failed: ${error.message}`);
        }
    }

    // Removed: _loadDirectVideo was unnecessary - inlined into _loadVideo

    /**
     * Check processing status from metadata
     */
    _checkProcessingStatus(metadata) {
        const status = metadata.status;
        const fileType = metadata.fileType;
        
        if (this.config.STATUS_CODES.ERROR.includes(status)) {
            return {
                canPlay: false,
                shouldRetry: false,
                message: metadata.message || 'Video processing failed',
                isError: true
            };
        }
        
        if (this.config.STATUS_CODES.PROCESSING.includes(status)) {
            return {
                canPlay: false,
                shouldRetry: true,
                message: 'Processing video...',
                progress: metadata.progress || 0
            };
        }
        
        if (status === 'uploaded' && fileType === 'file') {
            // Direct file upload, ready to play
            return { canPlay: true };
        }
        
        if (this.config.STATUS_CODES.READY.includes(status)) {
            // Processed video, ready to play
            return { canPlay: true };
        }
        
        // Unknown status, assume ready
        this.debugLogger.warn('Unknown processing status, assuming ready', { status, fileType });
        return { canPlay: true };
    }

    /**
     * Determine file type from filename extension (matches backend logic)
     */
    _getFileType(filename) {
        const extension = filename.split('.').pop().toLowerCase();

        if (this.config.FILE_EXTENSIONS.VIDEO.includes(extension)) {
            return 'video';
        } else if (this.config.FILE_EXTENSIONS.AUDIO.includes(extension)) {
            return 'audio';
        } else if (this.config.FILE_EXTENSIONS.IMAGE.includes(extension)) {
            return 'image';
        } else {
            // Default to file for unknown types
            return 'file';
        }
    }

    /**
     * Construct streaming URL from metadata
     */
    _constructStreamingURL(metadata, originalUrl) {
        const urlConstructor = new StreamingUrlConstructor(metadata, originalUrl, this.config);
        return urlConstructor.constructUrl();
    }

    // Removed: Simplified to use single URL without fallbacks
    
    /**
     * Test URL accessibility and content type
     */
    async _testUrlAccess(url) {
        try {


            const response = await fetch(url, {
                method: 'HEAD',
                credentials: 'include'
            });

            const contentType = response.headers.get('content-type');
            const contentLength = response.headers.get('content-length');

            // Enhanced content type checking for streaming endpoints
            const isVideoContent = contentType && (
                this.config.CONTENT_TYPES.VIDEO.some(type => contentType.startsWith(type)) ||
                this.config.CONTENT_TYPES.AUDIO.some(type => contentType.startsWith(type)) ||
                this.config.CONTENT_TYPES.STREAMING.some(type => contentType.startsWith(type))
            );

            // Accept more status codes for streaming
            const accessible = (response.ok || response.status === 206 || response.status === 416) && isVideoContent;



            // Special handling for 404s on streaming endpoints - log for debugging
            if (response.status === 404 && url.includes('/api/admin/secure-upload/')) {
                this.debugLogger.error('Streaming endpoint returned 404 - check backend route configuration', {
                    url: url,
                    expectedBackendRoute: 'Should match /api/admin/secure-upload/{public|private}/:type/:key',
                    troubleshooting: 'Verify portal-core backend is running and routes are registered'
                });
            }

            return accessible;
        } catch (error) {
            this.debugLogger.streaming('URL access test failed', {
                url: url,
                error: error.message,
                isNetworkError: error.name === 'TypeError' && error.message.includes('fetch')
            });
            return false;
        }
    }
    
    /**
     * Create streaming player using HLS/DASH
     */
    async _createStreamingPlayer(container, loadingMsg, originalUrl, streamingUrl, metadata) {
        this.debugLogger.streaming('Creating streaming player', { streamingUrl });

        try {
            // Determine if it's audio or video
            const isAudio = streamingUrl.includes('/audio/') ||
                           (metadata && metadata.fileType === 'audio') ||
                           streamingUrl.match(/\.(mp3|wav|ogg|aac)(\?|$)/i);

            // Create video/audio element
            const mediaElement = this._createMediaElement(originalUrl, streamingUrl, isAudio, metadata);

            // Replace loading message with media element
            loadingMsg.replaceWith(mediaElement);

            // Initialize streaming
            this._initializeStreaming(mediaElement, streamingUrl);

            // Mark as processed
            this.processedVideos.add(originalUrl);
            this.processingVideos.delete(originalUrl);

            this.debugLogger.streaming('Streaming player created', { streamingUrl });

        } catch (error) {
            this.debugLogger.error('❌ Failed to create streaming player', {
                originalUrl,
                streamingUrl,
                error: error.message,
                stack: error.stack
            });

            // Clean up processing state
            this.processingVideos.delete(originalUrl);
            throw error;
        }
    }

    /**
     * Create media element (video or audio)
     */
    _createMediaElement(originalUrl, streamingUrl, isAudio, metadata) {
        const elementType = isAudio ? 'audio' : 'video';
        
        const mediaStyles = isAudio ? this.config.MEDIA_STYLES.AUDIO : this.config.MEDIA_STYLES.VIDEO;
        
        const mediaElement = $(`<${elementType}>`, {
            controls: true,
            crossorigin: 'anonymous',
            playsinline: true,
            preload: 'metadata',
            'data-video-url': originalUrl,
            'data-streaming-url': streamingUrl,
            'data-streaming-type': 'hls-dash',
            css: mediaStyles
        });
        
        // Add poster for video if available
        if (!isAudio && metadata && streamingUrl.includes('/video/')) {
            const posterUrl = streamingUrl.replace('/video/', '/poster/').split('.')[0];
            mediaElement.attr('data-poster', posterUrl);
        }
        
        // Essential playback events only
        mediaElement.on('loadedmetadata', () => {
            this.debugLogger.streaming('Metadata loaded');
        });

        mediaElement.on('canplay', () => {
            this.debugLogger.streaming('Can play');
        });

        // Remove timeupdate spam - only log first successful play

        // Remove progress spam - only log errors
        mediaElement.on('error', () => {
            const mediaEl = mediaElement[0];
            const error = mediaEl.error;
            
            // Detailed error mapping
            const errorTypes = this.config.ERROR_TYPES;
            
            this.debugLogger.error('Media playback error', {
                url: originalUrl,
                streamingUrl: streamingUrl,
                errorCode: error ? error.code : 'unknown',
                errorType: error ? errorTypes[error.code] || 'unknown' : 'unknown',
                errorMessage: error ? error.message : 'unknown',
                networkState: mediaEl.networkState,
                readyState: mediaEl.readyState,
                currentSrc: mediaEl.currentSrc
            });
        });



        return mediaElement;
    }

    /**
     * Initialize streaming with HLS/DASH using MediaPlayerFactory
     */
    _initializeStreaming(mediaElement, streamingUrl) {
        this.debugLogger.streaming('Initializing streaming', { streamingUrl });
        
        // Handle Apple devices with native HLS support
        if (this._isAppleDevice() && (streamingUrl.includes('.m3u8') || streamingUrl.includes('mpegurl'))) {
            mediaElement[0].src = streamingUrl;
            return;
        }
        
        // Use MediaPlayerFactory for all other cases
        this.mediaPlayerFactory.createPlayer(streamingUrl, mediaElement);
    }


    /**
     * Check if device is Apple
     */
    _isAppleDevice() {
        return /iPad|iPhone|iPod/.test(navigator.userAgent) ||
               (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
    }

    /**
     * Get human-readable ready state text for debugging
     */
    _getReadyStateText(readyState) {
        return this.config.READY_STATES[readyState] || `UNKNOWN(${readyState})`;
    }

    /**
     * Get human-readable network state text for debugging
     */
    _getNetworkStateText(networkState) {
        return this.config.NETWORK_STATES[networkState] || `UNKNOWN(${networkState})`;
    }

    /**
     * Show processing status for videos still being processed
     * Uses consistent blue UI theme with CSS classes from openkb.scss
     */
    _showProcessingStatus(loadingMsg, statusInfo) {
        let statusHtml;

        if (statusInfo.isError) {
            // Error state - red theme
            statusHtml = `
                <div class="ph-alert ph-alert_danger">
                    <div class="ph-alert__title">${statusInfo.message}</div>
                    <div class="ph-alert__description">Video processing failed. Please try uploading again.</div>
                </div>
            `;
        } else {
            /* 
                // const progress = statusInfo.progress || 0;
                // <div class="video-processing-header">
                //     <div class="video-processing-spinner"></div>
                //     <span class="video-processing-title">${statusInfo.message}</span>
                //     ${progress > 0 ? `<span class="video-processing-percentage">${progress}%</span>` : ''}
                // </div>
                // ${progress > 0 ? `
                //     <div class="video-processing-progress">
                //         <div class="video-processing-progress-bar" style="width: ${progress}%;"></div>
                //     </div>
                // ` : ''}
                // <small class="video-processing-message"></small>
            */
            // Processing state - consistent blue theme
            statusHtml = `
                <div class="video-processing-status ph-alert ph-alert_info">
                    <div class="ph-alert__title"><div class="ph-spinner"></div>${statusInfo.message}</div> 
                    <div class="ph-alert__description">
                        <div>You can save this file and close this tab. Feel free to return later to view the video once processing is complete.<div>
                    </div> 
                </div>
            `;
        }

        loadingMsg.html(statusHtml);

    }

    /**
     * Show error message with retry option
     */
    _showError(loadingMsg, errorMessage) {
        const container = loadingMsg.closest('.video-embed-container');
        const videoUrl = container.attr('data-video-url');
        
        const errorHtml = `
            <div class="video-error-status" style="padding: 1rem; color: #721c24; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px;">
                <div style="margin-bottom: 8px;">
                    <strong>Error:</strong> ${errorMessage}
                </div>
                <div style="margin-bottom: 8px;">
                    <small>Check browser console for detailed logs</small>
                </div>
                ${videoUrl ? `
                <button class="btn btn-sm btn-primary retry-video-btn" data-video-url="${videoUrl}" style="font-size: 12px; padding: 4px 8px;">
                    <i class="fa fa-refresh" style="margin-right: 4px;"></i>Retry Video
                </button>
                ` : ''}
            </div>
        `;
        
        loadingMsg.html(errorHtml);
        loadingMsg.removeClass('video-loading-placeholder').addClass('video-error');
        
        // Add retry functionality
        if (videoUrl) {
            loadingMsg.find('.retry-video-btn').on('click', () => {
                this._retryVideo(container, videoUrl);
            });
        }
    }
    
    /**
     * Retry video processing
     */
    _retryVideo(container, videoUrl) {
        this.debugLogger.info('Retrying video processing', { videoUrl });
        
        // Clear from processed/processing sets to allow retry
        this.processedVideos.delete(videoUrl);
        this.processingVideos.delete(videoUrl);
        
        // Replace error with loading placeholder
        container.find('.video-error-status').replaceWith(this._createPlaceholder());
        
        // Restart video processing
        this.processingVideos.add(videoUrl);
        setTimeout(() => {
            this._loadVideo(container, videoUrl);
        }, 100);
    }
    
    /**
     * Handle processing timeout for long-running video processing
     */
    _handleProcessingTimeout(container, loadingMsg, videoUrl) {
        this.debugLogger.warn('Video processing timeout', { videoUrl });
        
        const timeoutHtml = `
            <div class="video-timeout-status" style="padding: 1rem; color: #856404; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px;">
                <div style="margin-bottom: 8px;">
                    <strong>Processing Taking Longer Than Expected</strong>
                </div>
                <div style="margin-bottom: 8px;">
                    <small>Your video is still being processed on the server. This may take a few more minutes for large files.</small>
                </div>
                <button class="btn btn-sm btn-secondary check-status-btn" data-video-url="${videoUrl}" style="font-size: 12px; padding: 4px 8px; margin-right: 8px;">
                    <i class="fa fa-clock-o" style="margin-right: 4px;"></i>Check Status
                </button>
                <button class="btn btn-sm btn-primary retry-video-btn" data-video-url="${videoUrl}" style="font-size: 12px; padding: 4px 8px;">
                    <i class="fa fa-refresh" style="margin-right: 4px;"></i>Retry
                </button>
            </div>
        `;
        
        loadingMsg.html(timeoutHtml);
        loadingMsg.removeClass('video-loading-placeholder').addClass('video-timeout');
        
        // Add event handlers
        loadingMsg.find('.retry-video-btn').on('click', () => {
            this._retryVideo(container, videoUrl);
        });
        
        loadingMsg.find('.check-status-btn').on('click', () => {
            // Replace timeout status with loading and try again
            loadingMsg.removeClass('video-timeout').addClass('video-loading-placeholder');
            loadingMsg.html('Checking video status...');
            
            setTimeout(() => {
                this._retryVideo(container, videoUrl);
            }, 100);
        });
        
        // Remove from processing set
        this.processingVideos.delete(videoUrl);
    }

    // Include all the standard methods from the original implementation
    _getTargetContainers(selectors) {
        if (typeof $ === 'undefined') {
            this.debugLogger.error('jQuery not available, cannot process videos');
            return { length: 0, find: () => ({ length: 0, each: () => {} }) };
        }
        
        const containers = [];
        selectors.forEach(selector => {
            const elements = $(selector);
            if (elements.length > 0) {
                containers.push(...elements.toArray());
                this.debugLogger.debug('Found containers', { selector, count: elements.length });
            }
        });
        

        
        return $(containers);
    }

    _processVideoContainer(container, delay = 0) {
        const videoUrl = container.attr('data-video-url');
        

        
        if (videoUrl && this.processingVideos.has(videoUrl)) {
            this.debugLogger.warn('Video already being processed, skipping', { videoUrl });
            return;
        }
        
        if (videoUrl && this.processedVideos.has(videoUrl)) {
            const existingMedia = container.find('video, audio');
            if (existingMedia.length > 0 && !existingMedia[0].error) {
                this.debugLogger.debug('Video already processed and working, skipping', { videoUrl });
                return;
            }
            this.debugLogger.info('Re-processing previously processed video', { videoUrl });
            this.processedVideos.delete(videoUrl);
        }
        
        // Clear existing content
        container.find('video, audio, .video-loading-placeholder, .video-error-status, .video-processing-status').remove();
        container.append(this._createPlaceholder());
        
        if (videoUrl) {
            this.processingVideos.add(videoUrl);
            setTimeout(() => {
                this._loadVideo(container, videoUrl);
            }, delay);
        }
    }

    _convertFileProxyToVideoContainer(element, delay = 0) {
        const url = element.attr('data-url');
        
        if (!url || this.processedVideos.has(url)) return;
        
        this.processedVideos.add(url);
        
        const videoContainer = $('<div>', {
            class: 'video-embed-container',
            'data-video-url': url,
            'data-video-type': 'streaming',
            'data-converted-from': 'file-to-proxy',
            css: { maxWidth: '100%', margin: '1rem 0' }
        });
        
        videoContainer.append(this._createPlaceholder());
        element.replaceWith(videoContainer);
        
        setTimeout(() => {
            this._loadVideo(videoContainer, url);
        }, delay);
    }

    
    _processUploadedMediaElement(element, delay = 0) {
        const $img = element.find('img');
        
        if ($img.length > 0) {
            const src = $img.attr('src');
            
            if (src && this._isVideoFile(src)) {
                this._convertUploadedMediaToVideoContainer(element, src, delay);
            }
        }
    }



    _isVideoFile(url) {
        const urlWithoutParams = url.split('?')[0];
        const extension = urlWithoutParams.split('.').pop().toLowerCase();
        return this.config.FILE_EXTENSIONS.ALL_VIDEO.includes(extension);
    }

    _convertUploadedMediaToVideoContainer(element, url, delay = 0) {
        if (this.processedVideos.has(url)) return;
        
        this.processedVideos.add(url);
        
        const videoContainer = $('<div>', {
            class: 'video-embed-container',
            'data-video-url': url,
            'data-video-type': 'streaming',
            'data-converted-from': 'uploaded-media',
            css: { maxWidth: '100%', margin: '1rem 0' }
        });
        
        videoContainer.append(this._createPlaceholder());
        element.replaceWith(videoContainer);
        
        setTimeout(() => {
            this._loadVideo(videoContainer, url);
        }, delay);
    }

    _createPlaceholder() {
        return $('<div>', {
            class: 'video-loading-placeholder',
            html: 'Loading video...',
            css: {
                padding: '1rem',
                color: '#666',
                textAlign: 'center'
            }
        });
    }

    clearVideoTracking(urlsToRemove = []) {
        this.debugLogger.info('Clearing video tracking', {
            urlsToRemove: urlsToRemove,
            currentProcessed: this.processedVideos.size,
            currentProcessing: this.processingVideos.size
        });
        
        if (urlsToRemove.length === 0) {
            const mediasWithErrors = $('video[data-streaming-url], audio[data-streaming-url]');
            mediasWithErrors.each((index, media) => {
                if (media.error || media.networkState === 3) {
                    const videoUrl = $(media).attr('data-video-url');
                    if (videoUrl) {
                        this.processedVideos.delete(videoUrl);
                        this.processingVideos.delete(videoUrl);
                        
                        // Cleanup streaming instances
                        const $media = $(media);
                        const hls = $media.data('hls');
                        const dash = $media.data('dash');
                        const plyr = $media.data('plyr');
                        
                        if (hls && hls.destroy) hls.destroy();
                        if (dash && dash.destroy) dash.destroy();
                        if (plyr && plyr.destroy) plyr.destroy();
                    }
                }
            });
        } else {
            urlsToRemove.forEach(url => {
                this.processedVideos.delete(url);
                this.processingVideos.delete(url);
            });
        }
        
        this.debugLogger.info('Video tracking cleared', {
            processedCount: this.processedVideos.size,
            processingCount: this.processingVideos.size
        });
    }
}

// Initialize streaming video renderer
window.VideoRenderer = StreamingVideoRenderer;
window.videoRenderer = new StreamingVideoRenderer();
window.VideoDebugLogger = VideoDebugLogger;

// Expose debugging controls
window.enableVideoDebug = function() {
    VideoDebugLogger.enabled = true;
    console.log('🎥 Streaming video debugging enabled');
};

window.disableVideoDebug = () => {
    VideoDebugLogger.enabled = false;
    console.log('Streaming video debugging disabled');
};

window.debugVideoPlayback = function() {
    const videos = $('video[data-streaming-url]');
    console.log(`🎬 Found ${videos.length} streaming videos:`);
    videos.each((index, video) => {
        const hasError = !!video.error;
        const status = hasError ? '❌ ERROR' : canPlay ? '✅ READY' : '⏳ LOADING';

        console.log(`${status} Video ${index + 1}:`, {
            src: video.currentSrc || video.src,
            readyState: `${video.readyState} (${window.videoRenderer._getReadyStateText(video.readyState)})`,
            networkState: `${video.networkState} (${window.videoRenderer._getNetworkStateText(video.networkState)})`,
            paused: video.paused,
            duration: video.duration,
            error: video.error ? `${video.error.code}: ${video.error.message}` : null
        });
    });
};

// Expose enhanced functions globally
window.activateAllVideoPlayers = function(options = {}) {
    return window.videoRenderer.activateVideoPlayers(options);
};

window.activateArticleVideoPlayers = function(options = {}) {
    return window.videoRenderer.activateVideoPlayers({
        targetContainers: ['#article-body', '.body_text'],
        ...options
    });
};

