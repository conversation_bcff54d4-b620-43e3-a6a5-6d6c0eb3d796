var fullyLoaded = false,
    firstLoad = true,
    appStarted = false;

const GOOGLE_STREETVIEW_KEY = 'AIzaSyDSTwkYTbnDBjC4_5j-doJ1J2jk5m9khys';

// remove deviceSearch from localStorage
localStorage.removeItem('deviceSearch');

$.getScript(
    `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_STREETVIEW_KEY}&libraries=places`,
    function () {
        $.getScript('https://www.google.com/jsapi', function () {
            (function (w, d, s, g, js, fjs) {
                g = w.gapi || (w.gapi = {});
                g.analytics = {
                    q: [],
                    ready: function (cb) {
                        this.q.push(cb);
                    },
                };
                js = d.createElement(s);
                fjs = d.getElementsByTagName(s)[0];
                js.src = 'https://apis.google.com/js/platform.js';
                fjs.parentNode.insertBefore(js, fjs);
                js.onload = function () {
                    g.load('analytics');
                };
            })(window, document, 'script');
            fullyLoaded = true;
        });
    }
);

var RegularFormSubmitFix;
RegularFormSubmitFix = function (app) {
    app.defaultCheckFormSubmission = this._checkFormSubmission;
    return (app._checkFormSubmission = function (form) {
        var $form, path, verb;
        $form = $(form);
        path = $form.attr('action');
        verb = this._getFormVerb($form);
        if (verb === 'get' && (!path || path[0] !== '#')) {
            return true;
        } else {
            return this.defaultCheckFormSubmission(form);
        }
    });
};

let sammy = $.sammy('main-container', function () {

    this.quiet = false; // Set quiet routes to false by default
    this.quietRoute = function (location) {
        sammy.quiet = true;
        this.setLocation(location);
    };
    // Prevents sammy from hooking form post/submits...
    this.use(RegularFormSubmitFix);

    this.getParam = (key) => {
        const location = this.getLocation();
        const expression = new RegExp(`${key}=(.*?)(&|&&|$)`, 'igm');
        const param = expression.exec(location);
        return param && param[1] ? param[1] : undefined;
    };

    // This function includes the logic for translations so we don't duplicate the translation over and over in the router...
    async function handleRoute(url, config = {}) {
        // catch routes the user should not be able to access...
        const isGloballyAllowedRoute = globallyAllowedRoutes.some((route) => location.pathname.includes(route));
        const isFacilityAllowedRoute = (selectedClubObj?.modules || []).some((module) => location.pathname.includes(module.baseUIPath));
        if (!sideMenuAdminMode && !isGloballyAllowedRoute && !isFacilityAllowedRoute) {
            console.warn('User does not have access to this route:', location.pathname);
            sammy.quiet = false;
            sammy.setLocation('/');
            return;
        }

        if (sammy.quiet) return;

        if(history.state?.designTagChange) return; // disable adding multiple state changes to keep actual URL in sync

        if (history.state?.tabChange || history.state?.filterChange) {
            return history.pushState(null, null);
        }

        // Ensure cleanup happens before loading new route
        await cleanupIframes();

        // Make the request and content processing asynchronous
        $.get(url).then(function (content) {

            setTimeout(() => { // Process content in a non-blocking way
                var processedContent = content.replace(/{{(.*?)}}/g, (matched) => {
                    return t(matched.replace(/{{|}}/g, ''));
                });

                $('main-container').html(processedContent);

                setSmallSidebarClasses();
                expandActiveMenuOnFirstLoad(location.pathname);
                fireClubSelectEvents(selectedID, gymMasterDomain, false);

                // Post load logic
                if (config.postLoadAction) {
                    config.postLoadAction();
                } else {
                    console.log('not loading postload logic at this time')
                }
            }, 0); // Timeout set to 0 to make this asynchronous
        });
    }

    // clear history designTagChange state on initial load
    if(history.state?.designTagChange) {
      history.state.designTagChange = false;
    }

    // ------------------------------------------------------------
    // MEMORY LEAK IMPROVEMENTS

    // remove any iframes from SPA's that are not currently in use / the current route.
    async function cleanupIframes() {
        // console.log('Starting iframe cleanup...');

        const iframeCleanup = async (selector, paths, name) => {
            // console.log(selector, paths, location.pathname, name);

            const $iframe = $(selector); // Directly select the iframe itself
            if ($iframe.length && !paths.some(path => location.pathname.startsWith(path))) {
                // console.log(`Reloading ${name} iframe before removal.`);

                if ($iframe[0] && $iframe[0].contentWindow) {
                    $iframe[0].contentWindow.location.reload();

                    // await sleep(5000); // Allow iframe to reload before removing

                    await new Promise(resolve => setTimeout(() => {
                        $iframe.remove(); // Remove the iframe itself
                        console.log(`Removed ${name} iframe.`);
                        resolve();
                    }, 200));
                } else {
                    // console.warn(`${name} iframe not found or already removed.`);
                    $iframe.remove(); // Ensure iframe is removed even if it doesn't have contentWindow
                }
            }
        };

        // Wait for all iframe cleanups to complete before proceeding
        await Promise.all([
            iframeCleanup('#cctvContainer', ['/cctv', '/cctv-ui'], 'CCTV'),
            iframeCleanup('#audioControlContainer', ['/audio-control', '/audio-control-ui'], 'Audio Control'),
            iframeCleanup('#coachingScreensContainer', ['/coaching-screens-ui'], 'Coaching Screens')
        ]);

        // console.log('Iframe cleanup completed.');
    }

    // ------------------------------------------------------------

    // Redirect root to dashboard...
    this.get('/', function () {
        const url = sideMenuAdminMode
            ? '/dashboard'
            : (selectedClubObj.redirectDashboard || '/dashboard');

        sammy.setLocation(url);
    });

    this.get('/dashboard', () => {
        if (
            !sideMenuAdminMode
            && selectedClubObj.redirectDashboard
            && selectedClubObj.redirectDashboard !== '/dashboard'
        ) {
            sammy.quiet = false;
            return sammy.setLocation(selectedClubObj.redirectDashboard);
        }

        handleRoute('/routes/dashboard.html', { postLoadAction: () => {
            if ($.sDashboard) {
                $(window).off('click', $.sDevices.base.selectedFilter, $.sDashboard.base.selectedFilter);
                $.sDashboard.base.init();
            }
        }})
    });


    // Basic Routes without Deeplinks
    // ------------------------------------------------------------------------
    // FACILITY OPERATIONS
    this.get('/notifications(/(.*))?', function () { handleRoute('/routes/notifications.html', { postLoadAction: () => { loadNotifications(selectedID); }}); });
    this.get('/global-schedule', () => handleRoute('/routes/global-schedule.html', { postLoadAction: sGlobalSchedule }));
    this.get('/workout-booking-leads', () => handleRoute('/routes/workout-booking-leads.html', { postLoadAction: initWBLeads }));
    this.get('/member-agreements', () => handleRoute('/routes/member-agreements.html', { postLoadAction: () => loadMemberAgreements(selectedID) }));
    this.get('/member-payments(/(.*))?', function () { handleRoute('/routes/member-payments.html'); });
    this.get('/training-camp', () => handleRoute('/routes/training-camp.html', { postLoadAction: () => loadTrainingCampMembersPage(selectedID) }));
    this.get('/training-camp(/(.*))?', () => handleRoute('/routes/training-camp-data.html', { postLoadAction: () => loadTrainingCampMemberInfo(selectedID) }));

    // Routes with more complex deeplinks / routes that need to handle multiple routes...
    this.get('/wiki(/(.*))?', function () {
        // This route will match both '/wiki' and '/wiki/anything'
        const wikiPath = this.params.splat[1];
        let iframePath = wikiPath ? decodeURIComponent(wikiPath.replace(/^#/, '')) : "/openkb";
        handleWikiRoute(iframePath);
    });

    // WEBSITE AND DIGITAL PROFILE
    this.get('/club-details', () => handleRoute('/routes/club-details.html'));
    this.get('/review-management', () => handleRoute('/routes/review-management.html'));
    this.get('/club-analytics', function () { handleRoute('/routes/club-analytics.html', { postLoadAction: injectGAClient }); });

    // SMART FACILITY TECH
    this.get('/audio-control', function () { handleRoute('/routes/audio-control.html', { postLoadAction: () => { sAudioControl(); $.sDevices.base.init(); }}); });
    this.get('/device-management', function () { handleRoute('/routes/device-management.html', { postLoadAction: () => { $(window).off('click', $.sDevices.base.selectedFilter, $.sDashboard.base.selectedFilter); $.sDevices.base.init(); }}); });

    this.get('/cctv', function () { handleRoute('/routes/cctv.html', { postLoadAction: () => { sCCTV(); $.sDevices.base.init(); }}); });
    this.get('/cctv-ui(/(.*))?', function () {
        const iframePath = decodeURIComponent(this.path.replace('/cctv-ui/#', '').replace('/cctv-ui', ''));
        handleRoute('/routes/cctv.html', { postLoadAction: () => {
            sCCTV(iframePath);
            $.sDevices.base.init();
        }});
    });

    this.get('/coaching-screens-ui(/(.*))?', function () {
        handleRoute('/routes/coaching-screens.html', { postLoadAction: () => {
            scoachingScreens();
            $.sDevices.base.init();
        }});
    });

    // SETTINGS / ACCOUNT
    this.get('/club-integrations', () => handleRoute('/routes/club-integrations.html', { postLoadAction: () => $.sExternalIntegrations.base.init() }));

    this.get('/agreements', function () { handleRoute('/routes/agreements.html', { postLoadAction: sAgreements }); });
    this.get('/club-kyc', function () { handleRoute('/routes/club-kyc.html'); });

    // ------------------------------------------------------------------------
    // ADMIN TOOLS
    this.get('/admin-portal(/(.*))?', () => handleRoute('/routes/admin-portal.html', { postLoadAction: sAdminPortal }));
    this.get('/holiday-management', () => handleRoute('/routes/holiday-management.html', { postLoadAction: initHolidayManagement }));
    this.get('/bulk-sms', function () { handleRoute('/routes/bulk-sms.html', { postLoadAction: sBulkSMS }); });
    this.get('/hardware-warranties', function () { handleRoute('/routes/hardware-warranties.html', { postLoadAction: sHardwareWarranties }); });
    this.get('/club-charges-and-fees(/(.*))?', function () { handleRoute('/routes/club-charges-and-fees.html', { postLoadAction: sApplicationFees }); });
    this.get('/saas-billing-admin(/(.*))?', function () { handleRoute('/routes/saas-billing-admin.html', { postLoadAction: sSaasBillingAdmin }); });
    this.get('/ph-admin', function () { handleRoute('/routes/ph-admin.html', { postLoadAction: () => $.phAdmin.base.init() }); });
    this.get('/kanban-settings', () => handleRoute('/routes/kanban.html', { postLoadAction: () => { setInternalRoute("settings"); skanban(); } }));
    this.get('/secure-uploader', function () { handleRoute('/routes/secure-uploader.html', { postLoadAction: initSecureUploader }); });
    this.get('/all-members', function () { handleRoute('/routes/all-members.html', { postLoadAction: loadAllMembers }); });
    this.get('/membership-management', function () { handleRoute('/routes/membership-management.html', { postLoadAction: loadMembershipManagement }); });
    this.get('/agreements-admin(/(.*))?', function () { handleRoute('/routes/agreements-admin.html', { postLoadAction: sAgreementsAdmin }); });
    this.get('/program-builder-ui(/(.*))?', function () { handleRoute('/routes/program-builder.html', { postLoadAction: sProgramBuilder }); });
    this.get('/website-management', function () { handleRoute('/routes/website-management.html', { postLoadAction: $.websiteManagement.base.init }); });
    this.get('/designer-admin', function () { handleRoute('/routes/designer-admin.html', { postLoadAction: sDesignerAdmin }); });
    this.get('/display-admin(/(.*))?', () => handleRoute('/routes/display-admin.html', { postLoadAction: sDisplayAdmin }));

    this.get('/wiki-admin(/(.*))?', function () {
        // This route will match both '/wiki' and '/wiki-admin/anything'
        const wikiPath = this.params.splat[1];
        let iframePath = wikiPath ? decodeURIComponent(wikiPath.replace(/^#/, '')) : "/openkb/settings";
        handleWikiRoute(iframePath);
    });


    // OLD LOGIC NOT YET LIVE
    // ------------------------------------------------------------------------
    this.get('/the-academy', () => handleRoute('/routes/the-academy.html', { postLoadAction: loadTheAcademy }));


    // NEW FEATURES STILL IN BETA:
    this.get('/project-management', () => handleRoute('/routes/kanban.html', { postLoadAction: () => { setDefaultRoute(); skanban(); } }));

    // ------------------------------------------------------------------------
    // Supporting Functions

    function handleWikiRoute(iframePath) {
        const iframePathWithClubId = updateHrefQuery({ club: selectedID }, iframePath, false);
        handleRoute('/routes/wiki.html', {
            postLoadAction: () => {
                console.info('wiki postload logic run', iframePathWithClubId);
 
                let $wikiContent = $('main-container #wikiContent');
                if ($wikiContent.length) {
                    let $wikiIframe = $wikiContent.find('iframe');
                    if ($wikiIframe.length) {
                        // If wiki iframe is still loaded in the DOM - load the URL directly
                        if ($wikiIframe.attr('src') !== iframePathWithClubId) {
                            console.log('change wiki url:', iframePathWithClubId)
                            $wikiIframe.attr('src', iframePathWithClubId);
                        }

                        sWiki();

                    } else {
                        $('main-container').html(content.replaceAll('%%WIKI_URL%%', iframePathWithClubId));
                        sWiki();
                    }
                } else {
                    lastWikiPageVisited = iframePath;
                    reloadWikiFrameBasedOnClubID(selectedID, "");
                    sWiki();
                }
            }
        });
    }

    this.get('/support(/(.*))?', () => handleRoute('/routes/support.html'));

    this.get('/designer(/(.*))?', function () {
        const tabUrl = this.params.splat[1] || 'designs';
        const { tag, id: designIdFromQuery } = getQueryStringsObject();

        const designTag = tag ? decodeURIComponent(tag) : false;

        handleRoute('/routes/designer.html', { postLoadAction: () => {
            const { designSearch, designId } = getQueryStringsObject();
            const decodedDesignSearch = designSearch ? decodeURIComponent(designSearch) : false;

            sMarketingVariables()
            sDesigns(designSearch ? 'designs-search' : tabUrl, designTag, designId, decodedDesignSearch);
            sEditor();
            sAssetsBrand();
            sAssetManager();
        }});
    });

    // Loads a "deeplinked" announcement.
    this.get('/announcements(/(.*))?', function () {
        const announcementId = this.params.splat[1];
        handleRoute('/routes/announcements.html', { postLoadAction: () => { sAnnouncements(announcementId); }});
    });

    this.get('/notifications/:id', function () {
        const notificationId = this.params.id;
        handleRoute('/routes/notification.html', { postLoadAction: () => { loadNotification(selectedID, notificationId); }});
    });

    this.get('/confluence-ui(/(.*))?', function () {
        const iframePath = decodeURIComponent(this.path.replace('/confluence-ui/#', '').replace('/confluence-ui', ''));
        handleRoute('/routes/confluence.html', { postLoadAction: () => sConfluenceUI(iframePath) });
    });

    this.get('/redash-ui(/(.*))?', function () {
        const iframePath = decodeURIComponent(this.path.replace('/redash-ui/#', '').replace('/redash-ui', ''));
        handleRoute('/routes/redash.html', { postLoadAction: () => sRedash(iframePath) });
    });

    this.get('/store-ui(/(.*))?', function () {
        const iframePath = decodeURIComponent(this.path.replace('/store-ui/#', '').replace('/store-ui', ''));
        handleRoute('/routes/store-v2.html', { postLoadAction: () => sStoreV2(iframePath) });
    });

    this.get('/store-admin-ui(/(.*))?', function () {
        const iframePath = decodeURIComponent(this.path.replace('/store-admin-ui/#', '').replace('/store-admin-ui', ''));
        handleRoute('/routes/store-v2.html', { postLoadAction: () => sStoreV2(iframePath) });
    });

    this.get('/mms-ui(/(.*))?', function () {
        const iframePath = decodeURIComponent(this.path.replace('/mms-ui/#', '').replace('/mms-ui', ''));
        handleRoute('/routes/member-management.html', { postLoadAction: () => sMMS(iframePath) });
    });

    // Loads the Audio Control homepage...
    this.get('/audio-control-ui(/(.*))?', function () {
        handleRoute('/routes/audio-control.html', { postLoadAction: () => {
            sAudioControl();
            $.sDevices.base.init();
        }});
    });

    // Loads the Smart Facility Health Check
    this.get('/health-check-ui(/(.*))?', function () {
        handleRoute('/routes/health-check.html', { postLoadAction: () => {
            shealthCheck();
            $.sDevices.base.init();
        }});
    });

    this.get('/404', function () {
        handleRoute('/routes/404.html');
    });



    this.get('/club-kyc/contact-details/(.*)?', function () {
        handleRoute('/routes/kyc-contact.html');
    });

    this.get('/facility-variables', function () {
        handleRoute('/routes/facility-variables.html');
    });

    // website management -->

    this.get('/release-history', function () {
        handleRoute('/routes/release-history.html', { postLoadAction: () => {
            sReleaseHistory();
            $.sDevices.base.init();
        }});
    });

    this.get('/saas-billing(/(.*))?', function () {
        handleRoute('/routes/saas-billing.html', { postLoadAction: () => {
            sSaaSBilling();
            $.sDevices.base.init();
        }});
    });

    // Todo: Remove this route once the new route is live...
    this.get('/legacy-saas-billing', function () {
        handleRoute('/routes/saas-billing.html', { postLoadAction: () => {
            sLegacySaaSBilling();
            $.sDevices.base.init();
        }});
    });


    this.notFound = function () {
        this.setLocation('/404');
    };

    function setSmallSidebarClasses() {
        // Hide any 'admin-only' elements that may be visible to user..
        if (!signedInUserData.isGlobalAdmin) $('.admin-only').hide();

        if (smallSidebar) {
            $('section.content').addClass('smallSidebarContent no-animate');
            $('.ph-page').addClass('smallSidebarContent');
            setTimeout(function () {
                $('section.content').removeClass('no-animate');
            }, 500);
        } else {
            if ($(window).width() < $.AdminBSB.options.leftSideBar.breakpointWidth) {
                if ($.AdminBSB.overLayOpen == true) {
                    $('.bars').click();
                }
            }
        }

        lockOutSidebarAndBanner();
    }
});

$(() => {
    (function sammyFirstLoad() {
        if (fullyLoaded && listAuthLoaded && socket) {
            setGMDomainHyperlink(gymMasterDomain);
            bindNavClickEvents();
            sammy.run();
            // firstLoad = false; // TODO: Deprecated as we have a new helper function for initial menu expansion: expandActiveMenuOnFirstLoad
        } else {
            setTimeout(sammyFirstLoad, 500);
        }
    })();

    sammy.after(function () {
        reDrawNavSelections();

        //set moment locale for each route...
        defineMomentLocale();

        // Render any Tooltip & Popup elements that are in our code...
        setTimeout(function () {
            // remove localStorage for device management when route is changed and route is not device-management
            if(!location.pathname.includes('device-management')) localStorage.removeItem('deviceSearch');
            try {
                $(function() {
                    $('[data-toggle="tooltip"]').tooltip({
                        container: 'body',
                        open: handleArrowTooltipOpen
                    });
                    $('[data-toggle="popover"]').popover();
                });
            } catch (e) {
                console.error("Error initializing tooltips/popovers:", e);
            }
        }, 2000);
    });
});

function bindNavClickEvents() {
    $('#leftsidebar a').each(function () {
        $(this).click(function () {
            sammy.quiet = false;
            // URL's known not to fire correctly when clicking from nav:
            if (this.pathname == '/announcements') {
                sammy.setLocation('/announcements');
                firstLoad = false;
            }
        });
    });
}

async function reDrawNavSelections() {
    
    // Wait incase we are still cleaning up iframes...
    await sleep(300);

    $('#leftsidebar a').each(function () {
        $(this).parent().removeClass('active');
        $(this).parent().parent().removeClass('active');
        $(this).parent().parent().parent().removeClass('active');
    });

    $('#leftsidebar a').each(function () {
        // Special logic for the wiki pages if they are coming from a deeplink...
        if (sammy?.last_route?.path.test(this.pathname)) {
            setActivePath(this);
        }
        if (sammy?.last_route?.path.toString() == '/\\/wiki\\/(.*)?$/' && this.pathname == '/wiki') {
            setActivePath(this);
        }
        if (sammy?.last_route?.path.toString() == '/\\/wb\\/(.*)?$/' && this.pathname == '/program-builder-ui') {
            setActivePath(this);
        }
        if (sammy?.last_route?.path.toString() == '/\\/store-ui\\/(.*)?$/' && this.pathname == '/store-ui') {
            setActivePath(this);
        }
        if (sammy?.last_route?.path.toString() == '/\\/designer\\/(.*)?$/' && this.pathname == '/designer') {
            setActivePath(this);
        }
        if (sammy?.last_route?.path.toString() == '/\\/club-details\\/(.*)?$/' && this.pathname == '/club-details') {
            setActivePath(this);
        }

        function setActivePath(el) {
            $(el).parent().addClass('active');
            $(el).parent().parent().addClass('active');
            $(el).parent().parent().parent().addClass('active');
            $(el).parent().parent().parent().find('a').addClass('toggled');
            $(el).addClass('toggled');

            // If we're loading a "deeplink" for the first time expand the menu (if we're loading the app - on that URL ...)
            console.log('Checking firstLoad inside setActivePath:', $(el).attr('href'), firstLoad);
            if (firstLoad) {
                let $topParent = $(el).parent().parent();
                console.log('First load condition met. Expanding menu for:', $topParent.prev());
                $('#leftsidebar > div > ul > li').each(function () {
                    $(this).find('.ml-menu').css('display', '');
                });
                if ($(el).parent().parent().hasClass('ml-menu')) {
                    $(el).parent().parent().slideDown();
                    // $(this).css("display", "block");
                }
                // Set firstLoad to false ONLY after successfully expanding the menu on the first load.
                firstLoad = false;
                console.log('firstLoad flag set to false inside setActivePath after expansion');
            }
        }
    });
}

function expandActiveMenuOnFirstLoad(currentPath) {
    console.log('expandActiveMenuOnFirstLoad', firstLoad);
    if (!firstLoad) return;

    console.log(`expandActiveMenuOnFirstLoad: Checking path ${currentPath}`);
    let linkFoundAndExpanded = false;
    $('#leftsidebar a').each(function () {
        const $el = $(this);
        // Basic path matching - adjust if needed for routes like /wiki/* etc.
        // Consider comparing normalized paths (e.g., removing trailing slashes)
        if (currentPath.includes(this.pathname)) {
            console.log(this.pathname, currentPath);
             let $parentLi = $el.parent(); // li
             let $parentUl = $parentLi.parent(); // ul.ml-menu
             let $topParentA = $parentUl.prev('a'); // a.menu-toggle

             // Check if it's a second-level menu item inside an expandable menu
             if ($parentUl.hasClass('ml-menu')) {
                  console.log('expandActiveMenuOnFirstLoad: Found matching link in submenu. Expanding:', $topParentA);
                  $parentUl.slideDown();
                  $topParentA.addClass('toggled');
                  $parentLi.addClass('active'); 
                  $topParentA.parent().addClass('active'); 

                  linkFoundAndExpanded = true;
                  return false;
             }
        }
    });

    if (linkFoundAndExpanded) {
        firstLoad = false; // Set flag to false only if expansion happened
        console.log('expandActiveMenuOnFirstLoad: Menu expanded and firstLoad set to false.');
    }
}

function listSammyRoutes() {
    var path = [];

    jQuery.each(sammy.routes, function (verb, routes) {
        jQuery.each(routes, function (i, route) {
            path.push(route.path);
        });
    });

    return path;
}

async function startApp() {
    appStarted = true;
    const preferences = await getUserPreferencesAPI();

    // Make sure we have at least access to one club before the onboarding tour/prompt appears...
    if (ClubsObj.length) {
        if (!preferences[0] || !preferences[1].tutorialWizardExited) {
            console.log("Welcome wizard currently disabled, and needs to be re-factored based on the users permissions.")
            // initiateWelcomeTourQuestion();
        }
    }

    if (preferences[0]) {
        userPreferences = preferences[1];

        // if user preferences notification pinned
        if (
            'notifications' in userPreferences
            && 'pinned' in userPreferences.notifications
            && userPreferences.notifications.pinned
        ) {
            toggleNotificationBar();
            $('#notification-pin').addClass('fa-rotate-90');
            $('#notif-bar-close').css('opacity', 0.4);
        }
    }
    // Trigger redraw once app has started...
    setTimeout(function () {
        window.dispatchEvent(new Event('resize'));
    }, 500);

    // Load hotjar feedback widget once startApp loaded...
    if (!devStage) {
        (function (h, o, t, j, a, r) {
            h.hj =
                h.hj ||
                function () {
                    (h.hj.q = h.hj.q || []).push(arguments);
                };
            h._hjSettings = { hjid: 2659899, hjsv: 6 };
            a = o.getElementsByTagName('head')[0];
            r = o.createElement('script');
            r.async = 1;
            r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
            a.appendChild(r);
        })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');

        // We need to wait for the hotjar widget to load before hooking it...
        setTimeout(function () {
            // Personally identify the user in Hotjar..
            var userId = signedinUser;
            window.hj('identify', userId, {
                Club: selectedID,
                email: userId,
            });

            // var preFillEmail = null;
            // $("._hj_feedback_container").click(function() {
            //     if(preFillEmail == null) {
            //         preFillEmail = setInterval(checkForEmailField, 500);
            //     }
            // });

            // function checkForEmailField() {
            //     if($('#email-address').length) {

            //         $('#email-address').val(signedinUser);
            //         $('#email-address').focus();
            //         $('#email-address').keyup();

            //         $("#email-address").trigger ( {
            //             type: 'keypress', keyCode: 65, which: 65, charCode: 65
            //         } );

            //         clearInterval(preFillEmail);
            //         preFillEmail = null;
            //     }
            // }
        }, 3000);
    }
}
